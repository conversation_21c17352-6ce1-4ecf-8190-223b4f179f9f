const { BadRequestError } = require('../errors');
const {
	createProjectsSchema,
	switchProjectLeadSchema,
	updateProjectSchema,
	deleteProjectSchema,
	getSingleProjectSchema,
	assignProjectToEmployeesSchema,
	createTaskSchema,
	updateTaskSchema,
	updateTaskStatusSchema,
	getEmployeesForTaskSchema,
	addCommentSchema,
	getTaskDetailsSchema,
	createTaskGroupSchema,
	updateTaskPositionSchema,
	getTaskGroupsSchema,
} = require('../schemas/projects-tasks.schema');

const createProjectMiddleware = (req, res, next) => {
	const result = createProjectsSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid Project Data', result.error.format());
	}
	next();
};

const switchProjectLeadMiddleware = (req, res, next) => {
	const result = switchProjectLeadSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid switch project lead Data',
			result.error.format()
		);
	}
	next();
};

const updateProjectMiddleware = (req, res, next) => {
	const result = updateProjectSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid update project Data',
			result.error.format()
		);
	}
	next();
};

const deleteProjectMiddleware = (req, res, next) => {
	const result = deleteProjectSchema.safeParse(req.params);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid delete project Data',
			result.error.format()
		);
	}
	next();
};

const getSingleProjectMiddleware = (req, res, next) => {
	const result = getSingleProjectSchema.safeParse(req.params);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid get single project Data',
			result.error.format()
		);
	}
	next();
};

const assignProjectToEmployeesMiddleware = (req, res, next) => {
	const result = assignProjectToEmployeesSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid assign project to employees Data',
			result.error.format()
		);
	}
	next();
};

const createTaskMiddleware = (req, res, next) => {
	const result = createTaskSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid create task Data',
			result.error.format()
		);
	}
	next();
};

const updateTaskMiddleware = (req, res, next) => {
	const result = updateTaskSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid update task Data',
			result.error.format()
		);
	}
	next();
};

const updateTaskStatusMiddleware = (req, res, next) => {
	const result = updateTaskStatusSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid update task status Data',
			result.error.format()
		);
	}
	next();
};

const updateTaskPositionMiddleware = (req, res, next) => {
	const result = updateTaskPositionSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid update task position Data',
			result.error.format()
		);
	}
	next();
};

const createTaskGroupMiddleware = (req, res, next) => {
	const result = createTaskGroupSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid create task group Data',
			result.error.format()
		);
	}
	next();
};

const getTaskGroupMiddleware = (req, res, next) => {
	const result = getTaskGroupsSchema.safeParse(req.params);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid get task group Data',
			result.error.format()
		);
	}
	next();
};

const getEmployeesForTaskMiddleware = (req, res, next) => {
	const result = getEmployeesForTaskSchema.safeParse(req.params);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid get employees for task Data',
			result.error.format()
		);
	}
	next();
};

const addCommentMiddleware = (req, res, next) => {
	const result = addCommentSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid add comment Data',
			result.error.format()
		);
	}
	next();
};
const getTaskDetailsMiddleware = (req, res, next) => {
	const result = getTaskDetailsSchema.safeParse(req.params);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid get task details Data',
			result.error.format()
		);
	}
	next();
};

module.exports = {
	createProjectMiddleware,
	switchProjectLeadMiddleware,
	updateProjectMiddleware,
	createTaskMiddleware,
	deleteProjectMiddleware,
	getSingleProjectMiddleware,
	assignProjectToEmployeesMiddleware,
	updateTaskMiddleware,
	updateTaskStatusMiddleware,
	updateTaskPositionMiddleware,
	createTaskGroupMiddleware,
	getEmployeesForTaskMiddleware,
	addCommentMiddleware,
	getTaskDetailsMiddleware,
	getTaskGroupMiddleware,
};
