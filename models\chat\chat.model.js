const mongoose = require('mongoose');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');
const mongooseDelete = require('mongoose-delete');

const ChatSchema = new mongoose.Schema({
	participants: [
		{
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
	],
	room: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'Room',
	},
	lastMessage: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'Message',
		autopopulate: {
			select: 'content createdAt',
		},
	},
	isGroup: {
		type: Boolean,
		default: false,
	},
});

ChatSchema.plugin(autoPopulate);
ChatSchema.plugin(aggregatePaginate);
ChatSchema.plugin(mongooseDelete, {
	overrideMethods: 'all',
	deletedAt: true,
	deletedBy: true,
});

module.exports = mongoose.model('Chat', ChatSchema);
