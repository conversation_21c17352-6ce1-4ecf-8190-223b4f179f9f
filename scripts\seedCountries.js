const fs = require('fs').promises;
const path = require('path');
const Country = require('../models/country.model');

async function seedCountries() {
	console.log('Starting country seeding...');

	try {
		const filePath = path.join(__dirname, '../data/countries.json');
		const countriesData = JSON.parse(await fs.readFile(filePath, 'utf8'));

		if (!countriesData.length) {
			console.log('No countries found in the file. Skipping seeding.');
			return;
		}
		console.log('total countries', countriesData.length);

		const countriesInDB = (await Country.find({}).select('numericCode')).map(
			(country) => country.numericCode
		);
		console.log('existing countries', countriesInDB.length);

		const remainingCountries = countriesData.filter(
			(country) => !countriesInDB.includes(country.numericCode)
		);
		console.log('remaining countries', remainingCountries.length);

		if (!remainingCountries.length) {
			console.log('all countries have already been seeded');
			return;
		}
		await Country.insertMany(countriesData);
		console.log(`Successfully seeded ${remainingCountries.length} countries.`);
	} catch (error) {
		console.error('Error during country seeding:', error);
	}
}

module.exports = seedCountries;
