const resetPasswordEmailTemplate = ({ email, resetToken, origin }) => {
	return `
    <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 50px auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .heading {
            font-size: 24px;
            color: #333333;
            margin-bottom: 20px;
        }
        .message {
            font-size: 16px;
            color: #666666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            font-size: 18px;
            color: #ffffff;
            background-color: #008e6d; /* Use your theme's primary color */
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin-top: 20px;
        }
        .footer {
            font-size: 12px;
            color: #999999;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="heading">Reset Your Password</div>
        <div class="message">
            We received a request to reset your password. Click the button below to proceed.
        </div>
        <a href="${origin}/reset-password?email=${email}&token=${resetToken}" class="button">Reset Password</a>
        <div class="message">
            If you did not request this, please ignore this email. For security reasons, this link will expire in the next 3 minutes.
        </div>
        <div class="footer">
            &copy; ${new Date().getFullYear()} Harp HR Software. All rights reserved.
        </div>
    </div>
</body>
</html>
    `;
};

module.exports = { resetPasswordEmailTemplate };
