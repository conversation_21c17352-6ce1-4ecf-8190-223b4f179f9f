// services/attendanceService.js
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
const { TimeLog } = require('../models');
dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * @function
 * @description Get the current time in a specific timezone
 * @param {string} tz - timezone string
 * @returns {dayjs.Dayjs} - the current time in the specified timezone
 */
const getCurrentTime = (tz) => {
	return dayjs().tz(tz);
};

/**
 * @function
 * @description Get the custom date in a specific timezone
 * @param {Object} options - options object
 * @param {string} options.tz - timezone string
 * @param {string|Date} options.customDate - custom date string or Date object
 * @returns {dayjs.Dayjs} - the custom date in the specified timezone
 */
const getCustomTime = ({ tz, customDate }) => {
	return dayjs(customDate).tz(tz);
};

/**
 * @function
 * @description Get the latest TimeLog for the user
 * @param {ObjectId} userId - the user ObjectId
 * @returns {Promise<TimeLog>} - the latest TimeLog
 */
const getTimeLogForToday = async (userId) => {
	return await TimeLog.findOne({
		userId,
		// createdAt: { $gte: dayjs().startOf('day').toISOString() },
	}).sort({ createdAt: -1 });
};

/**
 * @function
 * @description Calculate the duration between two dates in minutes
 * @param {string|Date} start - start date string or Date object
 * @param {string|Date} end - end date string or Date object
 * @returns {number} - the duration in minutes
 */
const calculateDuration = (start, end) => {
	return dayjs(end).diff(dayjs(start), 'minute');
};

const formatTimeForClockOut = (dateString, timeString, tz) => {
	const date = dayjs(dateString).format('YYYY-MM-DD');
	const fullDateTime = `${date} ${timeString}`; // e.g., '2025-06-08 20:00'

	return dayjs.tz(fullDateTime, 'YYYY-MM-DD HH:mm', tz);
};

module.exports = {
	getCurrentTime,
	getCustomTime,
	getTimeLogForToday,
	calculateDuration,
	formatTimeForClockOut,
};
