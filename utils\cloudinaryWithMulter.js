const cloudinary = require('cloudinary').v2;
const fs = require('fs');
const mime = require('mime-types');
const multer = require('multer');
const dayjs = require('dayjs');
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const { BadRequestError } = require('../errors');
const { logger } = require('./logger');

cloudinary.config({
	cloud_name: process.env.CLOUD_NAME,
	api_key: process.env.CLOUD_API_KEY,
	api_secret: process.env.CLOUD_API_SECRET,
});

const storage = new CloudinaryStorage({
	cloudinary,
	params: (req, file) => {
		const isVideo = file.mimetype.startsWith('video/');
		return {
			folder: `${process.env.CLOUD_FOLDER_NAME}-tasks-media`,
			resource_type: isVideo ? 'video' : 'image',
			public_id: `${dayjs().format('YYYY-MM-DD')}-${file.originalname.split('.')[0]}`,
		};
	},
	allowed_formats: ['jpg', 'png', 'mp4', 'webm', 'mkv', 'mov'],
});

const uploadTasksMedia = multer({
	storage,
	fileSize: 100 * 1024 * 1024,
	fileFilter: (req, file, cb) => {
		const allowedTypes = [
			'image/jpeg',
			'image/png',
			'video/mp4',
			'video/webm',
			'video/quicktime', // .mov
		];
		if (allowedTypes.includes(file.mimetype)) cb(null, true);
		else cb(new BadRequestError('Invalid file type'));
	},
});

// module.exports = uploadTasksMedia;

const detectResourceType = (mimeType) => {
	if (mimeType.startsWith('image/')) return 'image';
	if (mimeType.startsWith('video/')) return 'video';
	return 'raw';
};

const uploadSingle = async (filePath, mimeType) => {
	const resourceType = detectResourceType(mimeType);
	// console.log(
	// `💻 ~ cloudinaryWithMulter.js:54 ~ uploadSingle ~ resourceType:`,
	// resourceType
	// );

	const result = await cloudinary.uploader.upload(filePath, {
		resource_type: resourceType,
		folder: process.env.CLOUD_FOLDER_NAME,
		overwrite: true,
	});
	// Use this in case of uploading video file larger than 100 MB
	// const result =
	// 	resourceType === 'video'
	// 		? await cloudinary.uploader.upload_large(filePath, {
	// 				resource_type: resourceType,
	// 				folder: process.env.CLOUD_FOLDER_NAME,
	// 				chunk_size: 6 * 1024 * 1024,
	// 				overwrite: true,
	// 			})
	// 		: await cloudinary.uploader.upload(filePath, {
	// 				resource_type: resourceType,
	// 				folder: process.env.CLOUD_FOLDER_NAME,
	// 				overwrite: true,
	// 			});

	fs.unlinkSync(filePath);
	return result.secure_url;
};

const uploadToCloudinary = async (files) => {
	const uploadedUrls = [];

	for (const file of files) {
		try {
			const mimeType = file.mimetype;
			// console.log(
			// `💻 ~ cloudinaryWithMulter.js:80 ~ uploadToCloudinary ~ mimeType:`,
			// mimeType
			// );
			const url = await uploadSingle(file.path, mimeType);
			const urlObject = {
				url: url,
				public_id: file.filename,
				resource_type: detectResourceType(mimeType),
			};
			uploadedUrls.push(urlObject);
		} catch (err) {
			logger.error('Upload error:', err);
			throw new BadRequestError('Invalid file type');
		}
	}

	return uploadedUrls;
};

module.exports = uploadToCloudinary;
