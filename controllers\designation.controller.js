const { StatusCodes } = require('http-status-codes');
const { NotFoundError, ConflictError, BadRequestError } = require('../errors');
const { CompanyDetails, Designation, Department } = require('../models');
const { APIResponse } = require('../utils');
const {
	getDesignationsPipeline,
} = require('../db/aggregations/company-details.aggregation');

const addDesignation = async (req, res) => {
	const { designations } = req.body;

	const company = await CompanyDetails.findOne({
		_id: req.user.companyId._id,
	});
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const existingDesignations = await Designation.find({
		name: { $in: designations.map((designation) => designation.name) },
		companyId: company._id,
		departmentId: {
			$in: designations.map((designation) => designation.departmentId),
		},
		deleted: false,
	});

	if (existingDesignations.length > 0) {
		throw new ConflictError('Designation already exists', existingDesignations);
	}

	const createdDesignations = [];
	for (const designation of designations) {
		const department = await Department.findById(designation.departmentId);
		if (!department) {
			throw new NotFoundError('Department not found', [
				`Department with id: ${designation.departmentId} not found`,
			]);
		}
		const createdDesignation = await Designation.create({
			name: designation.name,
			companyId: department.companyId,
			departmentId: department._id,
			businessUnitId: department.businessUnitId,
		});

		createdDesignations.push(createdDesignation._id);
	}

	res
		.status(StatusCodes.CREATED)
		.json(
			new APIResponse(
				StatusCodes.CREATED,
				'Designation added successfully',
				createdDesignations
			)
		);
};

const getAllDesignations = async (req, res) => {
	const company = await CompanyDetails.findOne({
		_id: req.user.companyId._id,
	});
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const page = parseInt(req.query.page) || 1;
	let limit = parseInt(req.query.limit) || 10;
	if (req.query.limit === 'all') {
		limit = await Designation.countDocuments({ companyId: company._id });
	}
	const options = { page, limit };
	const designationAggregateQuery = Designation.aggregate(
		getDesignationsPipeline(company._id)
	);
	const result = await Designation.aggregatePaginate(
		designationAggregateQuery,
		options
	);

	if (result.docs === 0) {
		throw new NotFoundError('Designations not found');
	}

	res.status(StatusCodes.OK);
	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'Success', {
			designations: result.docs,
			totalPages: result.totalPages,
			currentPage: result.page,
			totalResults: result.totalDocs,
		})
	);
};

const updateDesignation = async (req, res) => {
	const company = await CompanyDetails.findOne({
		_id: req.user.companyId._id,
	});
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const department = await Department.findById(req.body.departmentId);
	if (!department) {
		throw new NotFoundError('Department not found');
	}

	const designationToUpdate = await Designation.findOne({
		_id: req.body._id,
		companyId: company._id,
		deleted: false,
	});

	if (!designationToUpdate) {
		throw new NotFoundError('Designation not found');
	}

	await Designation.updateOne({ _id: req.body._id }, { $set: req.body });

	const updatedDesignation = await Designation.findById(req.body._id);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(
				StatusCodes.OK,
				'Designation updated successfully',
				updatedDesignation
			)
		);
};

const deleteDesignation = async (req, res) => {
	const { designationIds } = req.body;

	if (!designationIds) {
		throw new BadRequestError('Please provide designationIds');
	}

	const company = await CompanyDetails.findOne({
		_id: req.user.companyId._id,
	});
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const designations = await Designation.find({
		_id: { $in: designationIds },
		companyId: company._id,
		deleted: false,
	});
	if (designations.length === 0) {
		throw new NotFoundError('Designations not found');
	}

	await Designation.updateMany(
		{ _id: { $in: designationIds } },
		{ $set: { deleted: true } }
	);

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Designations deleted successfully'));
};

module.exports = {
	getAllDesignations,
	addDesignation,
	updateDesignation,
	deleteDesignation,
};
