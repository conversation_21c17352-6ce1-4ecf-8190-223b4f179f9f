const winston = require('winston');
const fs = require('fs');
const path = require('path');
const DailyRotateFile = require('winston-daily-rotate-file');
const { MongoDB } = require('winston-mongodb');
const { combine, timestamp, errors, align, printf, json } = winston.format;

const logPath = path.join(__dirname, process.env.LOG_PATH || '../logs');
try {
	if (!fs.existsSync(logPath)) {
		fs.mkdirSync(logPath);
	}
} catch (error) {
	console.log(
		`file: logger.js:15 - Failed to create log directory at ${logPath}:`,
		error
	);
}

const performanceFilter = winston.format((info) => {
	if (info.isPerformanceLog) return info;
	return false;
});

const logger = winston.createLogger({
	level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
	format: combine(
		timestamp({
			format: 'DD-MM-YYYY hh:mm:ss.SSS A',
		}),
		errors({ stack: true }),
		printf(({ timestamp, level, message, stack, durationMs, ...meta }) => {
			const userInfo = meta.user ? `user: ${JSON.stringify(meta.user)}` : '';
			const durationInfo = durationMs ? `duration: ${durationMs}ms` : '';
			return `[${timestamp}] [${level}: ${message}] [${userInfo}] [${durationInfo}] [${
				stack ? '\n' + stack : ''
			}]`;
		})
	),
	exitOnError: process.env.NODE_ENV === 'production',
	exceptionHandlers: [
		new winston.transports.File({ filename: `${logPath}/exceptions.log` }),
	],
	rejectionHandlers: [
		new winston.transports.File({ filename: `${logPath}/rejections.log` }),
	],
	transports: [
		new DailyRotateFile({
			filename: `${logPath}/errors-%DATE%.log`,
			datePattern: 'YYYY-MM-DD',
			maxSize: '20m',
			maxFiles: '14d',
			level: 'error',
		}),
		new DailyRotateFile({
			filename: `${logPath}/info-%DATE%.log`,
			datePattern: 'YYYY-MM-DD',
			maxSize: '20m',
			maxFiles: '14d',
			level: 'info',
		}),
		...(process.env.NODE_ENV !== 'production'
			? [new winston.transports.Console({ format: winston.format.simple() })]
			: []),
		new MongoDB({
			level: 'info',
			db:
				process.env.NODE_ENV === 'production'
					? `${process.env.MONGO_URI_PRODUCTION}/${process.env.DB_NAME_PRODUCTION}`
					: `${process.env.MONGO_URI_DEVELOPMENT}/${process.env.DB_NAME_DEVELOPMENT}`,
			collection: 'info_logs',
		}),
		new MongoDB({
			level: 'error',
			db:
				process.env.NODE_ENV === 'production'
					? `${process.env.MONGO_URI_PRODUCTION}/${process.env.DB_NAME_PRODUCTION}`
					: `${process.env.MONGO_URI_DEVELOPMENT}/${process.env.DB_NAME_DEVELOPMENT}`,
			collection: 'error_logs',
		}),
		new MongoDB({
			level: 'info',
			db:
				process.env.NODE_ENV === 'production'
					? process.env.MONGO_URI_PRODUCTION
					: process.env.MONGO_URI_DEVELOPMENT,
			collection: 'process_logs',
			format: combine(performanceFilter()),
		}),
	],
});

class PerformanceLog {
	constructor({ message, user, path, method }) {
		this.isPerformanceLog = true;
		this.message = message;
		this.user = user;
		this.path = path;
		this.method = method;
	}
}

module.exports = { logger, PerformanceLog };
