const mongoose = require('mongoose');

const timezoneSchema = new mongoose.Schema({
	zoneName: { type: String },
	gmtOffset: { type: Number },
	gmtOffsetName: { type: String },
	abbreviation: { type: String },
	tzName: { type: String },
});

const citySchema = new mongoose.Schema(
	{
		name: { type: String },
		latitude: { type: Number, min: -90, max: 90 },
		longitude: { type: Number, min: -180, max: 180 },
	},
	{ timestamps: true }
);

const stateSchema = new mongoose.Schema(
	{
		name: { type: String },
		stateCode: { type: String, index: true }, // Indexed for fast lookup
		latitude: { type: Number, min: -90, max: 90 },
		longitude: { type: Number, min: -180, max: 180 },
		type: { type: String },
		cities: [citySchema], // Embedded city documents
	},
	{ timestamps: true }
);

const countrySchema = new mongoose.Schema(
	{
		name: { type: String, index: true },
		iso3: { type: String },
		iso2: { type: String },
		numericCode: { type: String },
		phoneCode: { type: String },
		capital: { type: String },
		currency: { type: String },
		currencyName: { type: String },
		currencySymbol: { type: String },
		native: { type: String },
		region: { type: String },
		subregion: { type: String },
		timezones: [timezoneSchema],
		states: [stateSchema],
	},
	{ timestamps: true }
);

module.exports = mongoose.model('Country', countrySchema);
