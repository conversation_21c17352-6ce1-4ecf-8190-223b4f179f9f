const { StatusCodes } = require('http-status-codes');
const { User } = require('../models');
const { APIResponse } = require('../utils');
const {
	getGenderAndEducationDataPipeline,
} = require('../db/aggregations/hr-module.aggregation');

const getGenderAndEducationData = async (req, res) => {
	const { clientAdminId, companyId } = req.user;

	const genderAndEducationData = await User.aggregate(
		getGenderAndEducationDataPipeline({
			clientAdminId,
			companyId: companyId._id,
		})
	);

	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'Success', {
			genderCounts: genderAndEducationData[0].genderCounts,
			educationCounts: genderAndEducationData[0].educationCounts,
			skillsCounts: genderAndEducationData[0].skillsCounts,
			totalBusinessUnits: genderAndEducationData[0].totalBusinessUnits,
			totalDepartments: genderAndEducationData[0].totalDepartments,
			totalDesignations: genderAndEducationData[0].totalDesignations,
			totalEmployees: genderAndEducationData[0].totalEmployees,
			employeeCount: genderAndEducationData[0].employeeCount,
		})
	);
};

module.exports = {
	getGenderAndEducationData,
};
