const express = require('express');
const router = express.Router();
const {
	login,
	logout,
	register,
	forgotPassword,
	resetPassword,
	verifyEmail,
	registerEmail,
	loginForMobile,
	getAuthenticatedUser,
	loginUsingQr,
	confirmQrSessionLogin,
} = require('../controllers/auth.controller');
const {
	authenticationMiddleware,
	authorizePermissions,
	loginMiddleware,
	registerEmailMiddleware,
	verifyEmailMiddleware,
	registerMiddleware,
	updatePasswordMiddleware,
	forgotPasswordMiddleware,
	mobileLoginMiddleware,
	qrMiddleware,
} = require('../middlewares');

/**
 * @route   GET /me
 * @desc    Fetch the details of the authenticated user
 * @access  Private (requires authentication)
 */
router.route('/me').get(authenticationMiddleware, getAuthenticatedUser);

/**
 * @route   POST /register
 * @desc    Initiate registration by sending verification email
 * @access  Public
 */
router
	.route('/register')
	.post(registerEmailMiddleware, registerEmail)
	.patch(registerMiddleware, register);

/**
 * @route   POST /login
 * @desc    Authenticate user and return token
 * @access  Public
 */
router.route('/login').post(loginMiddleware, login);

/**
 * @route   POST /mobile/login
 * @desc    Authenticate mobile user and return token
 * @access  Public
 */
router.route('/mobile/login').post(mobileLoginMiddleware, loginForMobile);

/**
 * @route   DELETE /logout
 * @desc    Log out authenticated user
 * @access  Private
 */
router.route('/logout').delete(authenticationMiddleware, logout);

/**
 * @route   POST /forgot-password
 * @desc    Send password reset instructions
 * @access  Public
 */
router.route('/forgot-password').post(forgotPasswordMiddleware, forgotPassword);

/**
 * @route   POST /reset-password
 * @desc    Reset user password
 * @access  Public (with valid reset token)
 */
router.route('/reset-password').post(updatePasswordMiddleware, resetPassword);

/**
 * @route   POST /verify-email
 * @desc    Verify user's email address
 * @access  Public (with valid verification token)
 */
router.route('/verify-email').post(verifyEmailMiddleware, verifyEmail);

router.route('/qr').post(authenticationMiddleware, qrMiddleware, loginUsingQr);
router.route('/confirm-qr').post(confirmQrSessionLogin);

module.exports = router;
