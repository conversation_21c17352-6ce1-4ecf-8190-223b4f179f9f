const express = require('express');
const router = express.Router();
const {
	authenticationMiddleware,
	authorizePermissions,
	employeePersonalDetailsMiddleware,
	uploadImage,
	uploadImageAndPdf,
	uploadExcelFile,
} = require('../middlewares');
const {
	createEmployeePersonalDetails,
	addEducationAndSkillsDetails,
	getAllEmployees,
	addContactDetails,
	getSingleEmployee,
	addEmploymentDetails,
	addEarningsDetails,
	getEmployeeProfileDetails,
	bulkEmployeeOnboarding,
	updatePersonalDetails,
	updateFamilyDetails,
	updateEducationDetails,
	updateExperienceDetails,
	updateContactDetails,
	updateEmploymentDetails,
	updateEarningsDetails,
	onboardEmployeeUsingLink,
	verifyLinkOnboardRequest,
	updateBenefitDetails,
} = require('../controllers/employee.controller');
const {
	educationAndSkillsMiddleware,
	employeeContactDetailsMiddleware,
	employeeEmploymentDetailsMiddleware,
	employeeEarningsDetailsMiddleware,
	updateEmployeePersonalDetailsMiddleware,
	updateEmployeeFamilyDetailsMiddleware,
	updateEmployeeEducationDetailsMiddleware,
	updateEmployeeExperienceDetailsMiddleware,
	updateEmployeeContactDetailsMiddleware,
	updateEmployeeEmploymentDetailsMiddleware,
	updateEmployeeEarningsDetailsMiddleware,
	onboardEmployeeUsingLinkMiddleware,
	verifyLinkOnboardRequestMiddleware,
	updateEmployeeBenefitDetailsMiddleware,
} = require('../middlewares/employeeRegistrationMiddleware');
const { userRoles } = require('../models/user.model');
const {
	getEditRequests,
	addEditRequest,
	updateEditRequestStatus,
	updateEditRequest,
	deleteEditRequests,
} = require('../controllers/edit-request.controller');
const {
	getEditRequestsMiddleware,
	addEditRequestMiddleware,
	updateEditRequestMiddleware,
	updateEditRequestStatusMiddleware,
	deleteEditRequestsMiddleware,
} = require('../middlewares/editRequest.middleware');

/**
 * @route   GET /edit-request
 * @desc    Get edit requests (all/by employee)
 * @access  Private (Authenticated)
 */
router
	.route('/edit-request')
	.get(authenticationMiddleware, getEditRequestsMiddleware, getEditRequests);

/**
 * @route   POST /edit-request
 * @desc    Add employee edit request
 * @access  Private (Authenticated)
 */
router
	.route('/edit-request')
	.post(authenticationMiddleware, addEditRequestMiddleware, addEditRequest);

/**
 * @route   PATCH /edit-request
 * @desc    Update employee edit request
 * @access  Private (Authenticated)
 */
router
	.route('/edit-request')
	.patch(
		authenticationMiddleware,
		updateEditRequestMiddleware,
		updateEditRequest
	);

/**
 * @route   PATCH /edit-request/status
 * @desc    Approve/Reject employee edit request
 * @access  Private (MODULE_ADMIN, CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/edit-request/status')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.MODULE_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		updateEditRequestStatusMiddleware,
		updateEditRequestStatus
	);

/**
 * @route   PATCH /edit-request/remove
 * @desc    Soft delete employee edit request
 * @access  Private (Authenticated)
 */
router
	.route('/edit-request/remove')
	.patch(
		authenticationMiddleware,
		deleteEditRequestsMiddleware,
		deleteEditRequests
	);

/**
 * @route   GET /
 * @desc    Get all employees
 * @access  Private (CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		getAllEmployees
	);

/**
 * @route   GET /:id
 * @desc    Get single employee by ID
 * @access  Private (CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/:id')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		getSingleEmployee
	);

/**
 * @route   GET /profile/:employeeId
 * @desc    Get employee profile details
 * @access  Private (Authenticated)
 */
router
	.route('/profile/:employeeId')
	.get(authenticationMiddleware, getEmployeeProfileDetails);

/**
 * @route   POST /register
 * @desc    Register employee personal details (with profile photo)
 * @access  Private (CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/register')
	.post(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		uploadImage.single('profilePhoto'),
		employeePersonalDetailsMiddleware,
		createEmployeePersonalDetails
	);

/**
 * @route   POST /qualifications
 * @desc    Add education and skills details (with documents)
 * @access  Private (CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/qualifications')
	.post(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		uploadImageAndPdf.array('documents', 10),
		educationAndSkillsMiddleware,
		addEducationAndSkillsDetails
	);

/**
 * @route   POST /contacts
 * @desc    Add employee contact details
 * @access  Private (CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/contacts')
	.post(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		employeeContactDetailsMiddleware,
		addContactDetails
	);

/**
 * @route   POST /employment
 * @desc    Add employee employment details
 * @access  Private (CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/employment')
	.post(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		employeeEmploymentDetailsMiddleware,
		addEmploymentDetails
	);

/**
 * @route   POST /earnings
 * @desc    Add employee earnings details
 * @access  Private (CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/earnings')
	.post(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		employeeEarningsDetailsMiddleware,
		addEarningsDetails
	);

/**
 * @route   POST /bulk
 * @desc    Bulk employee onboarding via Excel file
 * @access  Private (CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/bulk')
	.post(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		uploadExcelFile.single('file'),
		bulkEmployeeOnboarding
	);

/**
 * @route   PATCH /personal-details
 * @desc    Update employee personal details
 * @access  Private (MODULE_ADMIN, CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/personal-details')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.MODULE_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		updateEmployeePersonalDetailsMiddleware,
		updatePersonalDetails
	);

/**
 * @route   PATCH /family-details
 * @desc    Update employee family details
 * @access  Private (MODULE_ADMIN, CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/family-details')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.MODULE_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		updateEmployeeFamilyDetailsMiddleware,
		updateFamilyDetails
	);

/**
 * @route   PATCH /education-details
 * @desc    Update employee education details
 * @access  Private (MODULE_ADMIN, CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/education-details')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.MODULE_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		updateEmployeeEducationDetailsMiddleware,
		updateEducationDetails
	);

/**
 * @route   PATCH /experience-details
 * @desc    Update employee experience details
 * @access  Private (MODULE_ADMIN, CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/experience-details')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.MODULE_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		updateEmployeeExperienceDetailsMiddleware,
		updateExperienceDetails
	);

/**
 * @route   PATCH /contact-details
 * @desc    Update employee contact details
 * @access  Private (MODULE_ADMIN, CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/contact-details')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.MODULE_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		updateEmployeeContactDetailsMiddleware,
		updateContactDetails
	);

/**
 * @route   PATCH /employment-details
 * @desc    Update employee employment details
 * @access  Private (MODULE_ADMIN, CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/employment-details')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.MODULE_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		updateEmployeeEmploymentDetailsMiddleware,
		updateEmploymentDetails
	);

/**
 * @route   PATCH /earnings-details
 * @desc    Update employee earnings details
 * @access  Private (MODULE_ADMIN, CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/earnings-details')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.MODULE_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		updateEmployeeEarningsDetailsMiddleware,
		updateEarningsDetails
	);

router
	.route('/onboarding-link')
	.post(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.MODULE_ADMIN
		),
		onboardEmployeeUsingLinkMiddleware,
		onboardEmployeeUsingLink
	);

router
	.route('/onboarding-link/personal-details')
	.post(
		authenticationMiddleware,
		uploadImage.single('profilePhoto'),
		employeePersonalDetailsMiddleware,
		createEmployeePersonalDetails
	);

/**
 * @route   POST /qualifications
 * @desc    Add education and skills details (with documents)
 * @access  Private (CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/onboarding-link/qualifications')
	.post(
		authenticationMiddleware,
		uploadImageAndPdf.array('documents', 10),
		educationAndSkillsMiddleware,
		addEducationAndSkillsDetails
	);

/**
 * @route   POST /contacts
 * @desc    Add employee contact details
 * @access  Private (CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/onboarding-link/contacts')
	.post(
		authenticationMiddleware,
		employeeContactDetailsMiddleware,
		addContactDetails
	);

router
	.route('/verify-link/:token')
	.get(verifyLinkOnboardRequestMiddleware, verifyLinkOnboardRequest);

/**
 * @route   PATCH /benefit-details
 * @desc    Update employee benefit details
 * @access  Private (MODULE_ADMIN, CLIENT_ADMIN, GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/benefit-details')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.MODULE_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.GLORIFIED_CLIENT_ADMIN
		),
		updateEmployeeBenefitDetailsMiddleware,
		updateBenefitDetails
	);

module.exports = router;
