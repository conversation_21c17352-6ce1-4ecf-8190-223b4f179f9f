{"name": "harphr-backend", "version": "1.0.0", "description": "TimeMachine <PERSON>end", "main": "index.js", "scripts": {"start": "cross-env NODE_ENV=production nodemon app.js", "dev": "cross-env NODE_ENV=development nodemon app.js", "format": "prettier --write ."}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.7.9", "bcryptjs": "^2.4.3", "cloudinary": "1.21.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "cross-env": "^7.0.3", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "express": "^4.19.2", "express-async-errors": "^3.1.1", "express-formidable": "^1.2.0", "express-rate-limit": "^7.2.0", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "jsonwebtoken": "^9.0.2", "mime-types": "^3.0.1", "mongoose": "^8.3.0", "mongoose-aggregate-paginate-v2": "^1.1.3", "mongoose-autopopulate": "^1.1.0", "mongoose-delete": "^1.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "node-cron": "^4.1.0", "nodemailer": "^6.9.13", "pm2": "^5.4.3", "rate-limiter": "^0.2.0", "socket.io": "^4.8.1", "validator": "^13.12.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "winston-mongodb": "^6.0.0", "xlsx": "^0.18.5", "xss-clean": "^0.1.4", "zod": "^3.24.1"}, "devDependencies": {"nodemon": "^3.1.0", "prettier": "^3.5.3"}}