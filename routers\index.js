const router = require('express').Router();

const authRouter = require('./auth.router');
const clientAdminRouter = require('./client-admin.router');
const employeeRouter = require('./employee.router');
const holidayGroupRouter = require('./holiday-group.router');
const businessUnitRouter = require('./business-unit.router');
const departmentRouter = require('./department.router');
const designationRouter = require('./designation.router');
const holidayRouter = require('./holiday.router');
const countryRouter = require('./country.router');
const companyDetailsRouter = require('./company-details.router');
const glorifiedClientAdminRouter = require('./glorified-client-admin.router');
const hrModuleRouter = require('./hr-module.router');
const moduleAdminRouter = require('./module-admin.router');
const superAdminPromoteClientRouter = require('./super-admin-routers/promote-client.router');
const chatRouter = require('./chat.router');
const messageRouter = require('./message.router');
const attendanceRouter = require('./attendance.router');
const shiftSettingsRouter = require('./shift-setting.router');
const projectsRouter = require('./projects.router');
const tasksRouter = require('./tasks.router');

router.use('/auth', authRouter);
router.use('/client-admin', clientAdminRouter);
router.use('/employees', employeeRouter);
router.use('/business-units', businessUnitRouter);
router.use('/departments', departmentRouter);
router.use('/designations', designationRouter);
router.use('/holidays', holidayRouter);
router.use('/holiday-groups', holidayGroupRouter);
router.use('/countries', countryRouter);
router.use('/company-details', companyDetailsRouter);
router.use('/glorified-client-admin', glorifiedClientAdminRouter);
router.use('/hr-module', hrModuleRouter);
router.use('/module-admins', moduleAdminRouter);
router.use('/super-admin', superAdminPromoteClientRouter);
router.use('/chat', chatRouter);
router.use('/message', messageRouter);
router.use('/attendance', attendanceRouter);
router.use('/shifts', shiftSettingsRouter);
router.use('/projects', projectsRouter);
router.use('/tasks', tasksRouter);

module.exports = router;
