function generateEmployeePasswordLinkTemplate({
	origin,
	token,
	email,
	expirationTime,
	companyName,
}) {
	return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Set Your Password</title>
  <style>
    body {
      font-family: 'Segoe UI', sans-serif;
      background-color: #f4f4f4;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 30px auto;
      background-color: #ffffff;
      border-radius: 10px;
      padding: 30px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    h2 {
      color: #333333;
    }
    p {
      color: #555555;
      line-height: 1.5;
    }
    .btn {
      display: inline-block;
      margin-top: 20px;
      padding: 12px 25px;
      background-color: #28a745;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      font-weight: bold;
    }
    .btn:hover {
      background-color: #218838;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>Welcome to ${companyName}!</h2>
    <p>Hello,</p>
    <p>Your employer has created your account. To get started, please click the button below to set your password and activate your account.</p>
    <a href="${origin}/reset-password?token=${token}&email=${email}" class="btn">Set My Password</a>
    <p>This link will expire in ${expirationTime}. If you didn’t expect this email, you can ignore it.</p>
    <p>Thanks,<br/>The ${companyName} Team</p>
  </div>
</body>
</html>
`;
}

module.exports = { generateEmployeePasswordLinkTemplate };
