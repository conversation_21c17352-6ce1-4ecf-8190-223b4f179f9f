// --- <PERSON>TP email configuration with port 587 ---
module.exports = {
	host: process.env.SMTP_HOST,
	port: 587,
	secure: false,
	auth: {
		user: process.env.SMTP_USERNAME,
		pass: process.env.SMTP_PASSWORD,
	},
};
// --- <PERSON><PERSON> email configuration with port 587 ---

// --- <PERSON><PERSON> email configuration with port 465 ---
// module.exports = {
// 	host: process.env.SMTP_HOST,
// 	port: 465,
// 	secure: true,
// 	auth: {
// 		user: process.env.SMTP_USERNAME,
// 		pass: process.env.SMTP_PASSWORD,
// 	},
// };
// --- <PERSON>TP email configuration with port 465 ---

// // --- Ethereal email configuration ---
// module.exports = {
// 	host: 'smtp.ethereal.email',
// 	port: 587,
// 	secure: false,
// 	auth: {
// 		user: process.env.ETHEREAL_EMAIL,
// 		pass: process.env.ETHEREAL_PASSWORD,
// 	},
// };
// // --- Ethereal email configuration ---
