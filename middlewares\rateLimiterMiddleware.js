// middlewares/rateLimiter.js

const rateLimit = require('express-rate-limit');
const { StatusCodes } = require('http-status-codes');
const { logger } = require('../utils');

const limiter = rateLimit({
	windowMs: 15 * 60 * 1000, // 15 minutes
	max: 150, // Limit each IP to 150 requests per window
	standardHeaders: true, // Add rate limit info to `RateLimit-*` headers
	legacyHeaders: false, // Disable `X-RateLimit-*` headers

	keyGenerator: (req) => {
		const ip = req.ip;

		// console.log(`[RateLimit] Request from IP: ${ip}`);

		return ip;
	},

	handler: (req, res, next) => {
		const ip = req.ip;
		console.warn(`[RateLimit] Too many requests from IP: ${ip}`);
		logger.error(`[RateLimit] Too many requests from IP: ${ip}`);
		res.status(StatusCodes.TOO_MANY_REQUESTS).json({
			message: 'Too many requests. Please try again later.',
			errors: {
				data: null,
				errors: ['Too many requests. Please try again later.'],
				success: false,
				status: StatusCodes.TOO_MANY_REQUESTS,
			},
		});
	},
});

module.exports = limiter;
