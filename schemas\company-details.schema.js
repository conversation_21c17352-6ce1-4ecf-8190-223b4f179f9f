const { z } = require('zod');

const addBusinessUnitSchema = z
	.object({
		businessUnits: z
			.array(
				z.object({
					name: z.string().nonempty('Business Unit name is required'),
					location: z.string().nonempty('Business Unit location is required'),
				})
			)
			.min(1, 'At least one business unit is required'),
	})
	.strict();

const updateBusinessUnitSchema = z
	.object({
		_id: z.string().nonempty('Business Unit ID is required'),
		name: z.string(),
		location: z.string(),
		admin: z.string(),
	})
	.partial()
	.strict();

const addDepartmentSchema = z
	.object({
		departments: z
			.array(
				z.object({
					name: z.string().nonempty('Department name is required'),
					businessUnitId: z.string().nonempty('Business Unit ID is required'),
				})
			)
			.min(1, 'At least one department is required'),
	})
	.strict();

const updateDepartmentSchema = z
	.object({
		_id: z.string().nonempty('Department ID is required'),
		name: z.string(),
		businessUnitId: z.string(),
		admin: z.string(),
	})
	.partial()
	.strict();

const addDesignationSchema = z
	.object({
		designations: z
			.array(
				z.object({
					name: z.string().nonempty('Designation name is required'),
					departmentId: z.string().nonempty('Department ID is required'),
				})
			)
			.min(1, 'At least one designation is required'),
	})
	.strict();

const updateDesignationSchema = z
	.object({
		_id: z.string().nonempty('Designation ID is required'),
		name: z.string().nonempty('Designation name is required'),
		departmentId: z.string().nonempty('Department ID is required'),
	})
	.strict();

const addApproverSchema = z
	.object({
		approverId: z.string().nonempty('Approver ID is required'),
		designationId: z.string().nonempty('Designation ID is required'),
	})
	.strict();

const updateApproverSchema = z
	.object({
		approverId: z.string().nonempty('Approver ID is required'),
		designationId: z.string().nonempty('Designation ID is required'),
	})
	.strict();

const deleteBusinessUnitSchema = z
	.object({
		businessUnitIds: z
			.array(
				z
					.string()
					.nonempty('Business Unit ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.min(1, 'At least one business unit ID is required'),
	})
	.strict();

const deleteDepartmentSchema = z
	.object({
		departmentIds: z
			.array(
				z
					.string()
					.nonempty('Department ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.min(1, 'At least one department ID is required'),
	})
	.strict();

const deleteDesignationSchema = z
	.object({
		designationIds: z
			.array(
				z
					.string()
					.nonempty('Designation ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.min(1, 'At least one designation ID is required'),
	})
	.strict();

const updateWorkParametersSchema = z.object({
	monthlySchedule: z.object({
		total: z.number().min(1, 'Total must be at least 1'),
		description: z.string().min(1, 'Description is required'),
	}),
	dailySchedule: z.object({
		total: z.number().min(1, 'Total must be at least 1'),
		description: z.string().min(1, 'Description is required'),
	}),
	hourlySchedule: z.object({
		total: z.number().min(1, 'Total must be at least 1'),
		description: z.string().min(1, 'Description is required'),
	}),
});

const addHolidayGroupSchema = z.object({
	name: z.string().nonempty().min(1, 'Holiday Group name is required'),
	assignment: z.object({
		businessUnit: z
			.array(
				z
					.string()
					.nonempty('Business Unit ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.optional(),
		department: z
			.array(
				z
					.string()
					.nonempty('Department ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.optional(),
		designation: z
			.array(
				z
					.string()
					.nonempty('Designation ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.optional(),
		employee: z
			.array(
				z
					.string()
					.nonempty('Employee ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.optional(),
	}),
	assignTo: z.object({
		label: z.enum(['businessUnit', 'department', 'designation', 'employee']),
		value: z
			.array(z.string().min(1, 'ID value is required'))
			.min(1, 'At least one ID value is required'),
	}),
});

const updateHolidayGroupSchema = z.object({
	_id: z.string().nonempty('Holiday Group ID is required'),
	name: z.string().nonempty().min(1, 'Holiday Group name is required'),
	assignment: z.object({
		businessUnit: z
			.array(
				z
					.string()
					.nonempty('Business Unit ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.optional(),
		department: z
			.array(
				z
					.string()
					.nonempty('Department ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.optional(),
		designation: z
			.array(
				z
					.string()
					.nonempty('Designation ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.optional(),
		employee: z
			.array(
				z
					.string()
					.nonempty('Employee ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.optional(),
	}),
	assignTo: z.object({
		label: z.enum(['businessUnit', 'department', 'designation', 'employee']),
		value: z
			.array(z.string().min(1, 'ID value is required'))
			.min(1, 'At least one ID value is required'),
	}),
});

const deleteHolidayGroupSchema = z
	.object({
		holidayGroupIds: z
			.array(
				z
					.string()
					.nonempty('Holiday Group ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.min(1, 'At least one holiday group ID is required'),
	})
	.strict();

const addHolidaySchema = z.object({
	holidayGroupId: z.string().nonempty('Holiday Group ID is required'),
	title: z.string().min(1, 'Holiday title is required'),
	startDate: z.preprocess((val) => new Date(val), z.date()),
	endDate: z.preprocess((val) => new Date(val), z.date()).optional(),
	icon: z.string().min(1, 'Holiday icon is required'),
});

const updateHolidaySchema = z.object({
	_id: z.string().nonempty('Holiday ID is required'),
	holidayGroupId: z.string().nonempty('Holiday Group ID is required'),
	title: z.string().min(1, 'Holiday title is required'),
	startDate: z.preprocess((val) => new Date(val), z.date()),
	endDate: z.preprocess((val) => new Date(val), z.date()).optional(),
	icon: z.string().min(1, 'Holiday icon is required'),
});

const deleteHolidaysSchema = z
	.object({
		holidayIds: z
			.array(
				z
					.string()
					.nonempty('Holiday ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.min(1, 'At least one holiday ID is required'),
	})
	.strict();

const copyHolidayGroupSchema = z.object({
	name: z.string().nonempty().min(1, 'Holiday Group name is required'),
	assignment: z.object({
		businessUnit: z
			.array(
				z
					.string()
					.nonempty('Business Unit ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.optional(),
		department: z
			.array(
				z
					.string()
					.nonempty('Department ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.optional(),
		designation: z
			.array(
				z
					.string()
					.nonempty('Designation ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.optional(),
		employee: z
			.array(
				z
					.string()
					.nonempty('Employee ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID')
			)
			.optional(),
	}),
	assignTo: z.object({
		label: z.enum(['businessUnit', 'department', 'designation', 'employee']),
		value: z
			.array(z.string().min(1, 'ID value is required'))
			.min(1, 'At least one ID value is required'),
	}),
	holidays: z.array(
		z.object({
			title: z.string().min(1, 'Holiday title is required'),
			startDate: z.preprocess((val) => new Date(val), z.date()),
			endDate: z.preprocess((val) => new Date(val), z.date()).optional(),
			icon: z.string().min(1, 'Holiday icon is required'),
		})
	),
});

module.exports = {
	addBusinessUnitSchema,
	addDepartmentSchema,
	addDesignationSchema,
	addApproverSchema,
	updateBusinessUnitSchema,
	updateDepartmentSchema,
	updateDesignationSchema,
	updateApproverSchema,
	deleteBusinessUnitSchema,
	deleteDepartmentSchema,
	deleteDesignationSchema,
	updateWorkParametersSchema,
	addHolidayGroupSchema,
	copyHolidayGroupSchema,
	updateHolidayGroupSchema,
	deleteHolidayGroupSchema,
	addHolidaySchema,
	updateHolidaySchema,
	deleteHolidaysSchema,
};
