// middlewares/clientIp.js

const getClientIp = (req) => {
	let ip = req.ip || req.connection?.remoteAddress || '';

	// If behind proxy, req.ip might be something like "::ffff:************"
	if (ip.startsWith('::ffff:')) {
		ip = ip.replace('::ffff:', '');
	}

	// Final fallback — try raw header
	if (!ip || ip === '::1' || ip === '127.0.0.1') {
		const forwarded = req.headers['x-forwarded-for'];
		// console.log(` getClientIp - forwarded:`, forwarded);
		if (forwarded) {
			ip = forwarded.split(',')[0].trim();
			// console.log(` getClientIp - forwarded ip:`, ip);
		}
	}

	return ip;
};

const clientIpMiddleware = (req, res, next) => {
	req.clientIp = getClientIp(req);
	next();
};

module.exports = clientIpMiddleware;
