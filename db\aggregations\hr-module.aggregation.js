const { convertToObjectId } = require('../../utils');

const getGenderAndEducationDataPipeline = ({ clientAdminId, companyId }) => {
	return [
		{
			$match: {
				clientAdminId: convertToObjectId(clientAdminId),
				companyId: convertToObjectId(companyId),
				deleted: false,
			},
		},
		{
			$facet: {
				// 1. Gender distribution
				genderData: [
					{
						$lookup: {
							from: 'personaldetails',
							localField: '_id',
							foreignField: 'userId',
							as: 'personalDetails',
						},
					},
					{
						$addFields: {
							personalDetails: {
								$first: '$personalDetails',
							},
						},
					},
					{
						$group: {
							_id: '$personalDetails.gender',
							count: { $sum: 1 },
						},
					},
					{ $project: { k: '$_id', v: '$count' } },
					{
						$group: {
							_id: null,
							data: { $push: { k: '$k', v: '$v' } },
						},
					},
					{
						$project: {
							_id: 0,
							genderCounts: {
								$arrayToObject: {
									$filter: {
										input: '$data',
										as: 'item',
										cond: {
											$ne: ['$$item.k', null],
										},
									},
								},
							},
						},
					},
				],

				// 2. Education distribution
				educationData: [
					{
						$lookup: {
							from: 'educationdetails',
							localField: '_id',
							foreignField: 'userId',
							as: 'educationDetails',
						},
					},
					{
						$addFields: {
							educationDetails: {
								$first: '$educationDetails',
							},
						},
					},
					{
						$group: {
							_id: '$educationDetails.qualification',
							count: { $sum: 1 },
						},
					},
					{ $project: { k: '$_id', v: '$count' } },
					{
						$group: {
							_id: null,
							data: { $push: { k: '$k', v: '$v' } },
						},
					},
					{
						$project: {
							_id: 0,
							educationCounts: {
								$arrayToObject: {
									$filter: {
										input: '$data',
										as: 'item',
										cond: {
											$ne: ['$$item.k', null],
										},
									},
								},
							},
						},
					},
				],

				// 3. Skills counts
				skillsData: [
					{
						$lookup: {
							from: 'skills',
							localField: '_id',
							foreignField: 'userId',
							as: 'skills',
						},
					},
					{ $unwind: '$skills' },
					{
						$unwind: {
							path: '$skills.hardSkills',
							preserveNullAndEmptyArrays: true,
						},
					},
					{
						$unwind: {
							path: '$skills.softSkills',
							preserveNullAndEmptyArrays: true,
						},
					},
					{
						$group: {
							_id: null,
							hardSkillsCount: {
								$sum: {
									$cond: [
										{
											$ifNull: ['$skills.hardSkills', false],
										},
										1,
										0,
									],
								},
							},
							softSkillsCount: {
								$sum: {
									$cond: [
										{
											$ifNull: ['$skills.softSkills', false],
										},
										1,
										0,
									],
								},
							},
						},
					},
					{
						$project: {
							_id: 0,
							skillsCounts: {
								hardSkills: '$hardSkillsCount',
								softSkills: '$softSkillsCount',
							},
						},
					},
				],

				// 4. Total employees
				totalEmployees: [
					{
						$group: {
							_id: null,
							totalEmployees: { $sum: 1 },
						},
					},
				],

				// 5. Total business units
				totalBusinessUnits: [
					{
						$lookup: {
							from: 'businessunits',
							let: { companyId: '$companyId' },
							pipeline: [
								{
									$match: {
										$expr: {
											$eq: ['$companyId', '$$companyId'],
										},
									},
								},
								{
									$group: {
										_id: null,
										totalBusinessUnits: { $sum: 1 },
									},
								},
							],
							as: 'businessUnitsData',
						},
					},
					{ $unwind: '$businessUnitsData' },
					{
						$project: {
							totalBusinessUnits: '$businessUnitsData.totalBusinessUnits',
						},
					},
				],

				// 6. Total departments
				totalDepartments: [
					{
						$lookup: {
							from: 'departments',
							let: { companyId: '$companyId' },
							pipeline: [
								{
									$match: {
										$expr: {
											$eq: ['$companyId', '$$companyId'],
										},
									},
								},
								{
									$group: {
										_id: null,
										totalDepartments: { $sum: 1 },
									},
								},
							],
							as: 'departmentsData',
						},
					},
					{ $unwind: '$departmentsData' },
					{
						$project: {
							totalDepartments: '$departmentsData.totalDepartments',
						},
					},
				],

				// 7. Total designations
				totalDesignations: [
					{
						$lookup: {
							from: 'designations',
							let: { companyId: '$companyId' },
							pipeline: [
								{
									$match: {
										$expr: {
											$eq: ['$companyId', '$$companyId'],
										},
									},
								},
								{
									$group: {
										_id: null,
										totalDesignations: { $sum: 1 },
									},
								},
							],
							as: 'designationsData',
						},
					},
					{ $unwind: '$designationsData' },
					{
						$project: {
							totalDesignations: '$designationsData.totalDesignations',
						},
					},
				],
			},
		},
		{
			$project: {
				genderCounts: {
					$arrayElemAt: ['$genderData.genderCounts', 0],
				},
				educationCounts: {
					$arrayElemAt: ['$educationData.educationCounts', 0],
				},
				skillsCounts: {
					$arrayElemAt: ['$skillsData.skillsCounts', 0],
				},
				totalEmployees: {
					$arrayElemAt: ['$totalEmployees.totalEmployees', 0],
				},
				totalBusinessUnits: {
					$arrayElemAt: ['$totalBusinessUnits.totalBusinessUnits', 0],
				},
				totalDepartments: {
					$arrayElemAt: ['$totalDepartments.totalDepartments', 0],
				},
				totalDesignations: {
					$arrayElemAt: ['$totalDesignations.totalDesignations', 0],
				},
			},
		},
	];
};

module.exports = {
	getGenderAndEducationDataPipeline,
};
