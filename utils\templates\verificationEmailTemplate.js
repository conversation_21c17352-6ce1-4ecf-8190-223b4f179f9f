const verificationEmailTemplate = (verificationToken) => {
	return `
    <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your OTP Code</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 50px auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .heading {
            font-size: 24px;
            color: #333333;
            margin-bottom: 20px;
        }
        .otp {
            font-size: 48px;
            font-weight: bold;
            color: #008e6d; /* Use your theme's primary color */
            margin-bottom: 30px;
        }
        .message {
            font-size: 16px;
            color: #666666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        .footer {
            font-size: 12px;
            color: #999999;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="heading">Thank You for Registering with Time Machine Software!</div>
        <div class="message">
            To complete your verification, please use the following One-Time Password (OTP):
        </div>
        <div class="otp">${verificationToken}</div>
        <div class="message">
            This code is valid for the next 3 minutes. Please enter it on the verification page to complete your registration.<br><br>
            <strong>Important:</strong> For your security, do not share this code with anyone.<br><br>
            Thank you!
        </div>
        <div class="footer">
            &copy; ${new Date().getFullYear()} Harp HR Software. All rights reserved.
        </div>
    </div>
</body>
</html>
    `;
};

module.exports = { verificationEmailTemplate };
