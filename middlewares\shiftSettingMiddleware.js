const { BadRequestError } = require('../errors');
const {
	createShiftSettingSchema,
	assignShiftSchema,
	updateShiftSettingSchema,
} = require('../schemas/shift-setting.schema');

const createShiftSettingMiddleware = (req, res, next) => {
	const result = createShiftSettingSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError(
			'Shift setting validation failed',
			result.error.format()
		);
	}
	next();
};

const updateShiftSettingMiddleware = (req, res, next) => {
	const result = updateShiftSettingSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError(
			'Update Shift setting validation failed',
			result.error.format()
		);
	}
	next();
};

const assignShiftsMiddleware = (req, res, next) => {
	const result = assignShiftSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError(
			'Shift assignment validation failed',
			result.error.format()
		);
	}
	next();
};

module.exports = {
	createShiftSettingMiddleware,
	assignShiftsMiddleware,
	updateShiftSettingMiddleware,
};
