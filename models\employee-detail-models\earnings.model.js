const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

const incrementalPaySchema = new mongoose.Schema(
	{
		amount: {
			type: Number,
		},
		date: {
			type: Date,
		},
	},
	{ _id: false }
);

const EarningsSchema = new mongoose.Schema({
	userId: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'User',
	},
	basicPay: {
		currency: {
			type: String,
			required: [true, 'Please provide currency'],
		},
		amount: {
			type: Number,
			required: [true, 'Please provide amount'],
		},
	},
	paymentMode: {
		type: String,
		enum: ['cash/cheque', 'bank'],
		required: [true, 'Please provide payment mode'],
	},

	frequency: {
		type: String,
		enum: ['daily', 'weekly', 'monthly'],
		required: [true, 'Please provide frequency'],
	},
	payBasis: {
		type: String,
		enum: ['hourly', 'daily', 'weekly', 'monthly'],
		required: [true, 'Please provide pay basis'],
	},
	dailyRate: {
		type: Number,
	},
	hourlyRate: {
		type: Number,
	},
	weeklyRate: {
		type: Number,
	},
	yearlyRate: {
		type: Number,
	},
	overtimeRate: {
		type: Number,
	},
	isSalaryAdvanceEligible: {
		type: Boolean,
		default: false,
	},
	salaryAdvance: {
		type: Number,
	},

	bankName: {
		type: String,
		maxLength: [100, 'Bank name must be under 100 characters'],
	},
	accountNumber: {
		type: String,
		minLength: [6, 'Account number must be at least 6 digits'],
		maxLength: [20, "Account number can't exceed 20 digits"],
		match: [/^\d+$/, 'Account number must contain only numbers'],
	},
	accountHolderName: {
		type: String,
		minLength: [2, 'Account holder name must be at least 2 characters'],
		maxLength: [100, 'Account holder name must be less than 100 characters'],
	},
	bankCode: {
		type: String,
		minLength: [3, 'Bank code must be at least 3 characters'],
		maxLength: [15, 'Bank code must be under 15 characters'],
	},
	swiftBIC: {
		type: String,
		match: [
			/^[A-Z0-9]{8,11}$/,
			'SWIFT/BIC must be 8 or 11 alphanumeric characters',
		],
	},
	branchCode: {
		type: String,
		minLength: [3, 'Branch code must be at least 3 characters'],
		maxLength: [15, 'Branch code must be under 15 characters'],
	},

	incrementalPay: [incrementalPaySchema],
});

EarningsSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
EarningsSchema.plugin(aggregatePaginate);
EarningsSchema.plugin(autoPopulate);

module.exports = new mongoose.model('Earnings', EarningsSchema);
