const { StatusCodes } = require('http-status-codes');
const { BadRequestError, ConflictError, NotFoundError } = require('../errors');
const {
	CompanyDetails,
	User,
	Country,
	Token,
	BusinessUnit,
	Department,
	UserFlags,
} = require('../models');
const {
	uploadFileToCloudinary,
	APIResponse,
	attachCookiesToResponse,
	convertToObjectId,
} = require('../utils');
const crypto = require('crypto');
const {
	generateDefaultShiftSetting,
} = require('../models/attendance-module-models/shifts-setting.model');

const onboardingStepOne = async (req, res) => {
	const existingCompanyDetails = await CompanyDetails.findOne({
		registration: req.body.registration,
	});

	const existingUser = await User.findById(req.user.userId);

	if (existingCompanyDetails || existingUser?.isOnboard) {
		throw new ConflictError('Company Details already exists');
	}

	const logoLocalPath = req.file?.path;
	if (!logoLocalPath) {
		throw new BadRequestError('Please provide logo', [
			'Logo local path not found',
		]);
	}

	const logoUrl = await uploadFileToCloudinary(logoLocalPath);
	if (!logoUrl) {
		throw new BadRequestError('Could not upload logo', ['Logo upload failed']);
	}

	const companyDetails = await CompanyDetails.create({
		owner: req.user.userId,
		clientAdmin: req.user.userId,
		logo: logoUrl,
		businessName: req.body.businessName,
		businessCountry: req.body.businessCountry,
		registration: req.body.registration,
		address: req.body.address,
	});

	existingUser.clientAdminId = req.user.userId;
	existingUser.companyId = companyDetails._id;
	existingUser.isOnboardStepOneComplete = true;
	await existingUser.save();

	await UserFlags.create({
		userId: req.user.userId,
	});

	let refreshToken = '';
	const existingToken = await Token.findOne({ user: existingUser._id });
	if (existingToken) {
		const { isValid } = existingToken;
		if (!isValid) {
			throw new UnauthorizedError('Authentication Failed, Contact Admin');
		}
		refreshToken = existingToken.refreshToken;
		attachCookiesToResponse({ res, user: existingUser, refreshToken });
		res
			.status(StatusCodes.OK)
			.json(new APIResponse(StatusCodes.OK, 'Login Successful'));
		return;
	}

	refreshToken = crypto.randomBytes(40).toString('hex');
	const userAgent = req.headers['user-agent'];
	const ip = req.ip;
	await Token.create({ refreshToken, ip, userAgent, user: existingUser._id });
	attachCookiesToResponse({
		res,
		user: existingUser,
		refreshToken,
		rememberMe,
	});

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'success', companyDetails));
};

const onboardingClientFinalStep = async (req, res) => {
	const { currency, timeFormat, dateFormat, branches } = req.body;

	const existingCompanyDetails = await CompanyDetails.findOne({
		owner: req.user.userId,
	});
	const owner = await User.findById(req.user.userId);

	if (existingCompanyDetails && owner.isOnboard) {
		throw new ConflictError(
			'Looks like you are already onboarded, Try logging in'
		);
	}

	const companyBranches = [];

	for (const branch of branches) {
		const newBranch = await BusinessUnit.create({
			name: branch.branchName,
			location: branch.branchLocation,
			companyId: existingCompanyDetails._id,
			owner: existingCompanyDetails.owner,
		});

		for (const department of branch.departments) {
			const newDepartment = await Department.create({
				name: department.departmentName,
				businessUnitId: newBranch._id,
				companyId: existingCompanyDetails._id,
				// owner: existingCompanyDetails.owner,
			});
		}
		companyBranches.push(newBranch._id);
	}

	existingCompanyDetails.currency = currency;
	existingCompanyDetails.timeFormat = timeFormat;
	existingCompanyDetails.dateFormat = dateFormat;
	await existingCompanyDetails.save();

	owner.isOnboard = true;
	await generateDefaultShiftSetting({ companyId: existingCompanyDetails._id });
	await owner.save();

	await Token.findOneAndDelete({ user: owner._id });
	refreshToken = crypto.randomBytes(40).toString('hex');
	const userAgent = req.headers['user-agent'];
	const ip = req.ip;
	await Token.create({ refreshToken, ip, userAgent, user: owner._id });
	attachCookiesToResponse({ res, user: owner, refreshToken });

	res.status(StatusCodes.CREATED).json(
		new APIResponse(StatusCodes.CREATED, 'Onboarding Successful', {
			existingCompanyDetails,
		})
	);
};

module.exports = {
	onboardingClientFinalStep,
	onboardingStepOne,
};
