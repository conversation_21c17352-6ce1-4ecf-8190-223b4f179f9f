const axios = require('axios');
const tempEmailDomains = require('../data/tempEmailList.json');

const validateEmail = async (email) => {
	const tempEmailSet = new Set(tempEmailDomains);
	const domain = email.split('@')[1];

	if (tempEmailSet.has(domain)) {
		return true;
	}
	const { data } = await axios.get(
		`https://open.kickbox.com/v1/disposable/${domain}`
	);
	// console.log(`validateEmail - data.disposable:`, data.disposable);
	if (data.disposable) {
		return true;
	}
	return false;
};

module.exports = { validateEmail };
