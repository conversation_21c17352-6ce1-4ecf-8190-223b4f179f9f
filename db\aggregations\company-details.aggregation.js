const { convertToObjectId } = require('../../utils');

const getPopulatedBusinessUnitsPipeline = (companyId) => {
	return [
		{
			$match: {
				companyId: convertToObjectId(companyId),
				deleted: false,
			},
		},
		{
			$lookup: {
				from: 'departments',
				localField: '_id',
				foreignField: 'businessUnitId',
				as: 'departments',
			},
		},
		{ $unwind: '$departments' },
		{
			$lookup: {
				from: 'designations',
				localField: 'departments._id',
				foreignField: 'departmentId',
				as: 'designations',
			},
		},
		{ $unwind: '$designations' },
		{
			$lookup: {
				from: 'employmentdetails',
				localField: 'designations._id',
				foreignField: 'designation',
				as: 'employees',
			},
		},
		{ $unwind: '$employees' },
		{
			$lookup: {
				from: 'personaldetails',
				localField: 'employees.userId',
				foreignField: 'userId',
				as: 'userDetails',
			},
		},
		{
			$addFields: {
				'employees.userName': {
					$arrayElemAt: ['$userDetails.nameOnNRIC', 0],
				},
			},
		},
		// Group employees by designation
		{
			$group: {
				_id: '$designations._id',
				name: { $first: '$designations.name' },
				departmentId: { $first: '$departments._id' },
				employees: {
					$push: {
						_id: '$employees._id',
						userId: '$employees.userId',
						userName: '$employees.userName',
					},
				},
			},
		},
		// Group designations by department
		{
			$group: {
				_id: '$departmentId',
				designations: {
					$push: {
						_id: '$_id',
						name: '$name',
						employees: '$employees',
					},
				},
			},
		},
		// Join back to get department and business unit info
		{
			$lookup: {
				from: 'departments',
				localField: '_id',
				foreignField: '_id',
				as: 'departmentInfo',
			},
		},
		{
			$unwind: '$departmentInfo',
		},
		{
			$group: {
				_id: '$departmentInfo.businessUnitId',
				departments: {
					$push: {
						_id: '$_id',
						name: '$departmentInfo.name',
						designations: '$designations',
					},
				},
			},
		},
		{
			$lookup: {
				from: 'businessunits',
				localField: '_id',
				foreignField: '_id',
				as: 'businessUnitInfo',
			},
		},
		{ $unwind: '$businessUnitInfo' },
		{
			$project: {
				_id: 1,
				name: '$businessUnitInfo.name',
				location: '$businessUnitInfo.location',
				departments: 1,
			},
		},
	];
};

const getDepartmentsPipeline = (companyId) => {
	return [
		{
			$match: {
				companyId: convertToObjectId(companyId),
				deleted: false,
			},
		},
		{
			$lookup: {
				from: 'businessunits',
				localField: 'businessUnitId',
				foreignField: '_id',
				as: 'businessUnit',
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				localField: 'admin',
				foreignField: 'userId',
				as: 'admin',
			},
		},
		{
			$addFields: {
				businessUnit: {
					$first: '$businessUnit',
				},
				admin: {
					$first: '$admin',
				},
			},
		},
		{
			$project: {
				name: 1,
				'admin.userId': 1,
				'admin.nameOnNRIC': 1,
				'businessUnit.name': 1,
				'businessUnit.location': 1,
				'businessUnit._id': 1,
			},
		},
	];
};

const getDesignationsPipeline = (companyId) => {
	return [
		{
			$match: {
				companyId: convertToObjectId(companyId),
				deleted: false,
			},
		},
		{
			$lookup: {
				from: 'departments',
				localField: 'departmentId',
				foreignField: '_id',
				as: 'department',
			},
		},
		{
			$lookup: {
				from: 'businessunits',
				localField: 'department.businessUnitId',
				foreignField: '_id',
				as: 'businessUnit',
			},
		},
		{
			$addFields: {
				department: {
					$first: '$department',
				},
				businessUnit: {
					$first: '$businessUnit',
				},
			},
		},
	];
};

const getHolidayGroupsPipeline = (companyId) => {
	return [
		{
			$match: {
				companyId: convertToObjectId(companyId),
				deleted: false,
			},
		},
		{
			$lookup: {
				from: 'holidays',
				localField: '_id',
				foreignField: 'holidayGroupId',
				as: 'holidays',
			},
		},
		{
			$addFields: {
				holidaysCount: { $size: '$holidays' },
			},
		},
	];
};

const getHolidayPipeline = (companyId) => {
	return [
		{
			$match: {
				companyId: convertToObjectId(companyId),
				deleted: false,
			},
		},
		{
			$lookup: {
				from: 'holidaygroups',
				localField: 'holidayGroupId',
				foreignField: '_id',
				as: 'holidayGroupName',
			},
		},
		{
			$addFields: {
				holidayGroupName: { $arrayElemAt: ['$holidayGroupName.name', 0] },
			},
		},
	];
};

const getBusinessUnitsPipeline = ({ companyId, owner }) => {
	return [
		{
			$match: {
				companyId: convertToObjectId(companyId),
				owner: convertToObjectId(owner),
				deleted: false,
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				localField: 'admin',
				foreignField: 'userId',
				as: 'admin',
			},
		},
		{
			$addFields: {
				admin: {
					$first: '$admin',
				},
			},
		},
		{
			$project: {
				_id: 1,
				companyId: 1,
				owner: 1,
				deleted: 1,
				name: 1,
				location: 1,
				'admin.nameOnNRIC': 1,
				'admin.userId': 1,
			},
		},
	];
};

module.exports = {
	getPopulatedBusinessUnitsPipeline,
	getDepartmentsPipeline,
	getDesignationsPipeline,
	getHolidayGroupsPipeline,
	getHolidayPipeline,
	getBusinessUnitsPipeline,
};
