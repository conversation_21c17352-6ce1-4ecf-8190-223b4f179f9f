const express = require('express');
const {
	createTask,
	updateTask,
	updateTaskStatus,
	getTasksByProjectId,
	getTasksByUserId,
	updateTaskPosition,
	createTaskGroup,
	getEmployeesForTask,
	addComments,
	getTaskDetails,
	getTaskGroups,
} = require('../controllers/projects-and-tasks-module-controllers/tasks.controller');
const {
	createTaskMiddleware,
	updateTaskMiddleware,
	updateTaskStatusMiddleware,
	updateTaskPositionMiddleware,
	createTaskGroupMiddleware,
	getEmployeesForTaskMiddleware,
	addCommentMiddleware,
	getTaskGroupMiddleware,
} = require('../middlewares/projectsAndTasksMiddleware');
const {
	authorizePermissions,
	authenticationMiddleware,
} = require('../middlewares');
const { userRoles } = require('../models/user.model');
const { uploadHandler } = require('../middlewares/multerMiddleware');
const router = express.Router();

router
	.route('/')
	.post(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN,
			userRoles.PROJECT_ADMIN
		),
		uploadHandler.array('media', 10),
		createTaskMiddleware,
		createTask
	);

router
	.route('/')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN,
			userRoles.PROJECT_ADMIN
		),
		updateTaskMiddleware,
		updateTask
	);

router
	.route('/update-status')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN,
			userRoles.PROJECT_ADMIN,
			userRoles.EMPLOYEE
		),
		updateTaskStatusMiddleware,
		updateTaskStatus
	);

router
	.route('/project/:projectId')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN,
			userRoles.PROJECT_ADMIN
		),
		getTasksByProjectId
	);

router
	.route('/employee/:userId')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN,
			userRoles.PROJECT_ADMIN,
			userRoles.EMPLOYEE
		),
		getTasksByUserId
	);

router
	.route('/groups/:projectId')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN,
			userRoles.PROJECT_ADMIN
		),
		getTaskGroupMiddleware,
		getTaskGroups
	);

router
	.route('/group')
	.post(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN,
			userRoles.PROJECT_ADMIN
		),
		createTaskGroupMiddleware,
		createTaskGroup
	)
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN,
			userRoles.PROJECT_ADMIN
		),
		updateTaskPositionMiddleware,
		updateTaskPosition
	);

router
	.route('/employees')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN,
			userRoles.PROJECT_ADMIN
		),
		getEmployeesForTaskMiddleware,
		getEmployeesForTask
	);

router
	.route('/comments')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN,
			userRoles.PROJECT_ADMIN
		),
		addCommentMiddleware,
		addComments
	);

router
	.route('/:taskId')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN,
			userRoles.PROJECT_ADMIN
		),
		getTaskDetails
	);

module.exports = router;
