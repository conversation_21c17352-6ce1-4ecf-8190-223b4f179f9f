const replicateDocs = (docs) => {
	if (!Array.isArray(docs)) {
		throw new Error('Input must be an array of documents');
	}

	const replicatedDocs = [];
	for (let i = 0; i < 100; i++) {
		// Create a deep copy of each document to avoid reference issues
		const copiedDocs = docs.map((doc) => JSON.parse(JSON.stringify(doc)));
		// Add a unique identifier to each document
		copiedDocs.forEach((doc) => {
			doc.replicaId = i;
		});
		replicatedDocs.push(...copiedDocs);
	}

	return replicatedDocs;
};

module.exports = replicateDocs;
