const {
	getModuleAdmins,
	createModuleAdmin,
	deleteModuleAdmin,
} = require('../controllers/module-admin.controller');
const {
	authenticationMiddleware,
	authorizePermissions,
	createModuleAdminMiddleware,
	deleteModuleAdminMiddleware,
} = require('../middlewares');
const { userRoles } = require('../models/user.model');

const router = require('express').Router();

/**
 * @route   GET /
 * @desc    Get all module admins
 * @access  Private (GLORIFIED_CLIENT_ADMIN, CLIENT_ADMIN)
 */
router
	.route('/')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN
		),
		getModuleAdmins
	);

/**
 * @route   POST /
 * @desc    Add a new module admin
 * @access  Private (GLORIFIED_CLIENT_ADMIN, CLIENT_ADMIN)
 */
router
	.route('/')
	.post(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN
		),
		createModuleAdminMiddleware,
		createModuleAdmin
	);

/**
 * @route   PATCH /
 * @desc    Remove a module admin
 * @access  Private (GLORIFIED_CLIENT_ADMIN, CLIENT_ADMIN)
 */
router
	.route('/')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN
		),
		deleteModuleAdminMiddleware,
		deleteModuleAdmin
	);

module.exports = router;
