const BadRequestError = require('./BadRequestError');
const ConflictError = require('./ConflictError');
const CustomError = require('./CustomError');
const ForbiddenAccessError = require('./ForbiddenAccess');
const InternalServerError = require('./InternalServerError');
const NotFoundError = require('./NotFoundError');
const UnauthorizedError = require('./UnauthorizedError');

module.exports = {
	CustomError,
	NotFoundError,
	BadRequestError,
	UnauthorizedError,
	ConflictError,
	ForbiddenAccessError,
	InternalServerError,
};
