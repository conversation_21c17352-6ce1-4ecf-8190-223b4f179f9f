const nodemailer = require('nodemailer');
const nodemailerConfig = require('./nodemailerConfig');
const { logger } = require('./logger');
const sendEmail = async ({ to, subject, html, text }) => {
	let testAccount = await nodemailer.createTestAccount();
	const transporter = nodemailer.createTransport(nodemailerConfig);
	// console.log(nodemailerConfig);
	const mailOptions = {
		from: `Valluva ${process.env.NOREPLY_EMAIL}`,
		to,
		subject,
		text,
		html,
	};

	const info = await transporter.sendMail(mailOptions, (error) => {
		if (error) {
			console.log(error);
			logger.error(error);
		}
	});
	// console.log(info);
	logger.info(`Email sent to ${to}`);
};

module.exports = sendEmail;
