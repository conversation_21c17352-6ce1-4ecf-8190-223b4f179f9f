const express = require('express');
const router = express.Router();

// Controllers
const {
	getAllBusinessUnits,
	addBusinessUnit,
	updateBusinessUnit,
	getPopulatedBusinessUnits,
	deleteBusinessUnit,
	getBusinessUnitDepartments,
} = require('../controllers/business-unit.controller');

// Middlewares
const {
	authenticationMiddleware,
	addBusinessUnitMiddleware,
	updateBusinessUnitMiddleware,
} = require('../middlewares');

const {
	deleteBusinessUnitMiddleware,
} = require('../middlewares/company-details.middleware');

/**
 * @route   GET /
 * @desc    Get all business units
 * @access  Private (Authenticated)
 */
router.route('/').get(authenticationMiddleware, getAllBusinessUnits);

/**
 * @route   POST /
 * @desc    Add a new business unit
 * @access  Private (Authenticated)
 */
router
	.route('/')
	.post(authenticationMiddleware, addBusinessUnitMiddleware, addBusinessUnit);

/**
 * @route   PATCH /
 * @desc    Update an existing business unit
 * @access  Private (Authenticated)
 */
router
	.route('/')
	.patch(
		authenticationMiddleware,
		updateBusinessUnitMiddleware,
		updateBusinessUnit
	);

/**
 * @route   GET /populate
 * @desc    Get business units with populated fields (e.g., related entities)
 * @access  Private (Authenticated)
 */
router
	.route('/populate')
	.get(authenticationMiddleware, getPopulatedBusinessUnits);

/**
 * @route   PATCH /remove
 * @desc    Soft delete a business unit
 * @access  Private (Authenticated)
 */
router
	.route('/remove')
	.patch(
		authenticationMiddleware,
		deleteBusinessUnitMiddleware,
		deleteBusinessUnit
	);

/**
 * @route   GET /:id/departments
 * @desc    Get all departments for a specific business unit by its ID
 * @access  Private (Authenticated)
 */
router
	.route('/:id/departments')
	.get(authenticationMiddleware, getBusinessUnitDepartments);

module.exports = router;
