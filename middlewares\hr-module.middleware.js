const { BadRequestError } = require('../errors');
const { createModuleAdminSchema } = require('../schemas/hr-module.schema');

const createModuleAdminMiddleware = (req, res, next) => {
	const result = createModuleAdminSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid request data', result.error.format());
	}
	next();
};

const deleteModuleAdminMiddleware = (req, res, next) => {
	const { moduleAdminIds } = req.body;
	if (!Array.isArray(moduleAdminIds) || moduleAdminIds.length === 0) {
		throw new BadRequestError(
			'Module admin IDs are required',
			result.error.format()
		);
	}
	next();
};

module.exports = {
	createModuleAdminMiddleware,
	deleteModuleAdminMiddleware,
};
