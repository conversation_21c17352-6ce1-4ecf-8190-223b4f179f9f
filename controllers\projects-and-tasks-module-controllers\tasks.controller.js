const { StatusCodes } = require('http-status-codes');
const { Project, Task, User, TaskGroup } = require('../../models');
const { NotFoundError, BadRequestError } = require('../../errors');
const { APIResponse, convertToObjectId } = require('../../utils');
const dayjs = require('dayjs');
const uploadToCloudinary = require('../../utils/cloudinaryWithMulter');

const createTask = async (req, res) => {
	const project = await Project.findOne({ _id: req.body.projectId });
	if (!project) {
		throw new NotFoundError('Project Not Found');
	}
	const assignedTo = await User.findOne({
		_id: req.body.assignedTo,
		projectsAssigned: req.body.projectId,
	});
	if (!assignedTo) {
		throw new NotFoundError('Assigned To Not Found');
	}
	if (dayjs(req.body.dueDate).isBefore(dayjs())) {
		throw new BadRequestError('Due Date cannot be in the past');
	}

	const mediaObjects = await uploadToCloudinary(req.files);

	const task = await Task.create({
		...req.body,
		assignedBy: req.user.userId,
		media: mediaObjects,
	});
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Task Created', task));
};

const updateTask = async (req, res) => {
	const task = await Task.findOneAndUpdate({ _id: req.body.taskId }, req.body);
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Task Updated', task));
};

const updateTaskStatus = async (req, res) => {
	const task = await Task.findOneAndUpdate(
		{ _id: req.body.taskId },
		{ $set: { status: req.body.status } }
	);
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Task Status Updated', task));
};

const deleteTask = async (req, res) => {
	const task = await Task.findOneAndDelete({ _id: req.params.taskId });
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Task Deleted', task));
};

const getTasksByProjectId = async (req, res) => {
	const tasks = await Task.find({ projectId: req.params.projectId });
	if (tasks.length === 0) {
		throw new NotFoundError('Tasks Not Found');
	}
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Success', tasks));
};

const getTasksByUserId = async (req, res) => {
	const tasks = await Task.find({ assignedTo: req.params.userId });
	if (tasks.length === 0) {
		throw new NotFoundError('Tasks Not Found');
	}
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Success', tasks));
};

const createTaskGroup = async (req, res) => {
	const taskGroup = await TaskGroup.create({
		companyId: req.user.companyId._id,
		...req.body,
	});
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Task Group Created', taskGroup));
};

const deleteTaskGroup = async (req, res) => {
	const taskGroup = await TaskGroup.findOneAndDelete({
		_id: req.params.taskGroupId,
	});
	if (!taskGroup) {
		throw new NotFoundError('Task Group Not Found');
	}
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Task Group Deleted', taskGroup));
};

const updateTaskPosition = async (req, res) => {
	// Check if the source and destination task groups are the same
	const isSourceAndDestinationSame =
		req.body.sourceGroupId === req.body.destinationGroupId;

	// Retrieve the task by ID

	const task = await Task.findById(req.body.taskId);
	if (!task) {
		// If task not found, throw an error
		throw new NotFoundError('Task Not Found');
	}

	// Retrieve the source task group by ID
	const sourceTaskGroup = await TaskGroup.findById(req.body.sourceGroupId);
	if (!sourceTaskGroup) {
		// If source task group not found, throw an error
		throw new NotFoundError('Source Task Group Not Found');
	}

	// Initialize destination task group as source task group
	let destinationTaskGroup = sourceTaskGroup;
	// If source and destination groups are different, fetch destination task group
	if (!isSourceAndDestinationSame) {
		destinationTaskGroup = await TaskGroup.findById(
			req.body.destinationGroupId
		);
		if (!destinationTaskGroup) {
			// If destination task group not found, throw an error
			throw new NotFoundError('Destination Task Group Not Found');
		}
	}

	// Check if the task is present at the given source index in the source task group
	if (sourceTaskGroup.taskIds.length <= req.body.sourceIndex) {
		// If not present, throw an error
		throw new BadRequestError('Task is not present at source index');
	}
	const isTaskPresentAtSourceIndex =
		sourceTaskGroup.taskIds[req.body.sourceIndex].toString() ===
		req.body.taskId;
	if (!isTaskPresentAtSourceIndex) {
		// If not present, throw an error
		throw new BadRequestError('Task is not present at source index');
	}

	// Get the length of task IDs in the destination task group
	const destinationGroupTasksLength = destinationTaskGroup.taskIds.length;

	// Remove the task from its current position in the source task group
	sourceTaskGroup.taskIds.splice(req.body.sourceIndex, 1);
	// Insert the task at the new position in the destination task group
	destinationTaskGroup.taskIds.splice(
		Math.min(req.body.destinationIndex, destinationGroupTasksLength),
		0,
		req.body.taskId
	);

	// Save the changes to the database
	if (isSourceAndDestinationSame) {
		// If the groups are the same, save the destination task group once
		await destinationTaskGroup.save();
	} else {
		// If the groups are different, save both source and destination task groups
		await destinationTaskGroup.save();
		await sourceTaskGroup.save();
	}

	// Send a successful response with the updated task
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Task Updated', task));
};

const getTaskGroups = async (req, res) => {
	const taskGroup = await TaskGroup.find({
		companyId: req.user.companyId._id,
		projectId: req.params.projectId,
	});

	if (taskGroup.length === 0) {
		throw new NotFoundError('Task Groups Not Found');
	}

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Success', taskGroup));
};

const getEmployeesForTask = async (req, res) => {
	const employees = await Project.aggregate([
		{
			$match: {
				_id: convertToObjectId(req.params.projectId),
			},
		},
		{
			$lookup: {
				from: 'users',
				localField: '_id',
				foreignField: 'projectsAssigned',
				as: 'assigned',
			},
		},
		{
			$unwind: {
				path: '$assigned',
				includeArrayIndex: 'string',
				preserveNullAndEmptyArrays: false,
			},
		},
		{
			$project: {
				_id: 0,
				userId: '$assigned._id',
				name: '$assigned.name',
				profilePhoto: '$assigned.profilePhoto',
				email: '$assigned.email',
			},
		},
	]);

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Success', employees));
};

const addComments = async (req, res) => {
	const task = await Task.findOneAndUpdate(
		{ _id: req.body.taskId },
		{
			$addToSet: {
				comments: {
					comment: req.body.comment,
					userId: req.user.userId,
					createdAt: dayjs().toDate(),
				},
			},
		}
	);
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Task Updated', task));
};

const getTaskDetails = async (req, res) => {
	console.log(
		`💻 ~ tasks.controller.js:201 ~ getTaskDetails ~ req.params.taskId:`,
		req.params.taskId
	);
	const task = await Task.aggregate([
		{
			$match: {
				_id: convertToObjectId(req.params.taskId),
			},
		},
		{
			$lookup: {
				from: 'users',
				let: { commentUserIds: '$comments.userId' },
				pipeline: [
					{
						$match: {
							$expr: {
								$in: ['$_id', '$$commentUserIds'],
							},
						},
					},
					{
						$project: {
							_id: 1,
							name: 1,
							profilePhoto: 1,
							email: 1,
						},
					},
				],
				as: 'commentUsers',
			},
		},
		{
			$addFields: {
				comments: {
					$map: {
						input: '$comments',
						as: 'comment',
						in: {
							_id: '$$comment._id',
							comment: '$$comment.comment',
							createdAt: '$$comment.createdAt',
							userId: '$$comment.userId',
							user: {
								$arrayElemAt: [
									{
										$filter: {
											input: '$commentUsers',
											as: 'user',
											cond: {
												$eq: ['$$user._id', '$$comment.userId'],
											},
										},
									},
									0,
								],
							},
						},
					},
				},
			},
		},
		{
			$project: {
				name: 1,
				description: 1,
				status: 1,
				assignedTo: 1,
				assignedBy: 1,
				projectId: 1,
				dueDate: 1,
				priority: 1,
				media: 1,
				deleted: 1,
				createdAt: 1,
				updatedAt: 1,
				comments: 1,
			},
		},
	]);

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Success', task[0]));
};

module.exports = {
	createTask,
	updateTask,
	updateTaskStatus,
	deleteTask,
	getTasksByProjectId,
	getTasksByUserId,
	createTaskGroup,
	deleteTaskGroup,
	updateTaskPosition,
	getEmployeesForTask,
	addComments,
	getTaskDetails,
	getTaskGroups,
};
