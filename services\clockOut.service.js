const dayjs = require('dayjs');
const { getCurrentTime, convertToObjectId } = require('../utils');
const { TimeLog, User } = require('../models');
const { formatTimeForClockOut } = require('../utils/attendanceTimeCalculation');

const tzAggregation = (userId) => {
	return [
		{
			$match: {
				_id: convertToObjectId(userId),
			},
		},
		{
			$lookup: {
				from: 'companydetails',
				localField: 'companyId',
				foreignField: '_id',
				as: 'company',
			},
		},
		{
			$addFields: {
				company: { $first: '$company' },
			},
		},
		{
			$project: {
				company: 1,
			},
		},
		{
			$lookup: {
				from: 'countries',
				localField: 'company.businessCountry',
				foreignField: '_id',
				as: 'country',
			},
		},
		{
			$addFields: {
				country: { $first: '$country' },
			},
		},
		{
			$addFields: {
				timezone: {
					$first: '$country.timezones.zoneName',
				},
			},
		},
		{
			$project: {
				timezone: 1,
			},
		},
	];
};

const clockOutGenericShiftEmployee = async () => {
	const yesterday = dayjs(
		dayjs().subtract(1, 'day').format('YYYY-MM-DD')
	).toISOString();
	const today = dayjs().toISOString();

	const timeLogs = await TimeLog.find({
		createdAt: { $gte: yesterday, $lt: today },
	});

	logsWithoutClockOut = timeLogs
		.map((log) => {
			if (
				!log.attendanceTime.find((entry) => entry.type === 'clockOut') &&
				log.attendanceTime.find((entry) => entry.type === 'clockIn')
			) {
				return log;
			}
		})
		.filter((log) => undefined !== log);

	for (log of logsWithoutClockOut) {
		const tz = await User.aggregate(tzAggregation(log.userId));
		// console.log(tz[0].timezone)
		const formattedEndTime = formatTimeForClockOut(
			log.createdAt,
			log.endTime,
			tz[0].timezone
		);

		const lastLog = log.attendanceTime[log.attendanceTime.length - 1];
		const hoursWorkedInMinutes = dayjs(lastLog.time).diff(
			formattedEndTime,
			'minute'
		);
		// console.log(hoursWorkedInMinutes)
		log.actualWorkingHoursInMinutes =
			log.actualWorkingHoursInMinutes || 0 + hoursWorkedInMinutes;
		log.attendanceTime.push({
			type: 'clockOut',
			time: formattedEndTime.toISOString(),
		});
		log.clockOutBy = 'system';
		await log.save();
		// console.log(formattedEndTime.toISOString())
	}
};

module.exports = {
	clockOutGenericShiftEmployee,
};
