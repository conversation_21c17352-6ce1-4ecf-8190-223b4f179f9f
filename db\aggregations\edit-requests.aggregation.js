const { convertToObjectId } = require('../../utils');

const getEditRequestsAggregation = ({ userId, status, section }) => {
	// TODO: If user is not an admin, they can only see their own edit requests
	// if (req.user.role > 5) {
	// 	// Regular employee
	// 	filter.userId = req.user._id;
	// }

	return [
		{
			$match: {
				deleted: false,
				...(userId && { userId: convertToObjectId(userId) }),
				...(status && { status }),
				...(section && { section }),
			},
		},
		{
			$sort: {
				submittedAt: -1,
			},
		},
		{
			$lookup: {
				from: 'users',
				localField: 'userId',
				foreignField: '_id',
				as: 'userId',
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				localField: 'userId._id',
				foreignField: 'userId',
				as: 'userPersonalDetails',
			},
		},
		{
			$lookup: {
				from: 'users',
				localField: 'approvedBy',
				foreignField: '_id',
				as: 'approvedBy',
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				localField: 'approvedBy._id',
				foreignField: 'userId',
				as: 'approvedByPersonalDetails',
			},
		},
		{
			$lookup: {
				from: 'users',
				localField: 'rejectedBy',
				foreignField: '_id',
				as: 'rejectedBy',
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				localField: 'rejectedBy._id',
				foreignField: 'userId',
				as: 'rejectedByPersonalDetails',
			},
		},
		{
			$addFields: {
				userId: { $first: '$userId' },
				userPersonalDetails: { $first: '$userPersonalDetails' },
				approvedBy: { $first: '$approvedBy' },
				approvedByPersonalDetails: { $first: '$approvedByPersonalDetails' },
				rejectedBy: { $first: '$rejectedBy' },
				rejectedByPersonalDetails: { $first: '$rejectedByPersonalDetails' },
			},
		},
		{
			$project: {
				oldData: 1,
				newData: 1,
				section: 1,
				status: 1,
				reason: 1,
				submittedAt: 1,
				approvedAt: 1,
				rejectedAt: 1,
				rejectionReason: 1,
				'userId._id': 1,
				'userId.email': 1,
				'userId.nameOnNRIC': '$userPersonalDetails.nameOnNRIC',
				'userId.employeeOrgId': '$userPersonalDetails.employeeOrgId',
				'approvedBy._id': 1,
				'approvedBy.email': 1,
				'approvedBy.nameOnNRIC': '$approvedByPersonalDetails.nameOnNRIC',
				'approvedBy.employeeOrgId': '$approvedByPersonalDetails.employeeOrgId',
				'rejectedBy._id': 1,
				'rejectedBy.email': 1,
				'rejectedBy.nameOnNRIC': '$rejectedByPersonalDetails.nameOnNRIC',
				'rejectedBy.employeeOrgId': '$rejectedByPersonalDetails.employeeOrgId',
			},
		},
	];
};

module.exports = {
	getEditRequestsAggregation,
};
