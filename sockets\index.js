const { Server } = require('socket.io');
const authNamespace = require('./namespaces/auth');
const chatNamespace = require('./namespaces/chat');
let io;

const initSocket = (server) => {
	// console.log(` initSocket - server:`, server);
	io = new Server(server, {
		cors: {
			origin: [
				process.env.FRONTEND_ORIGIN,
				process.env.DEVELOPMENT_ORIGIN,
				process.env.STAGING_ORIGIN,
			],
			methods: ['GET', 'POST'],
			credentials: true,
		},
	});

	authNamespace(io.of('/auth'));
	chatNamespace(io.of('/chat'));
};

const setSocket = () => {
	// if (!io) {
	// 	throw new Error(
	// 		'Socket.io server is not initialized. Call initSocket first.'
	// 	);
	// }
	return io;
};

module.exports = {
	initSocket,
	setSocket,
};
