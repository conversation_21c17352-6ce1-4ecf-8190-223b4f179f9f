const {
	authenticationMiddleware,
	authorizePermissions,
	loginMiddleware,
	registerEmailMiddleware,
	verifyEmailMiddleware,
	registerMiddleware,
	updatePasswordMiddleware,
	forgotPasswordMiddleware,
	mobileLoginMiddleware,
	qrMiddleware,
} = require('./authenticationMiddleware');
const { validateEmail } = require('./emailValidationMiddleware');
const {
	employeePersonalDetailsMiddleware,
} = require('./employeeRegistrationMiddleware');
const errorHandlingMiddleware = require('./errorHandlingMiddleware');
const {
	uploadImage,
	uploadImageAndPdf,
	uploadExcelFile,
} = require('./multerMiddleware');
const notFoundMiddleware = require('./notFoundMiddleware');
const {
	onboardingStepOneMiddleware,
	onboardingFinalStepMiddleware,
} = require('./onboardingMiddleware');
const {
	validateCountryId,
	validateCountryData,
	validateDebouncedInputCities,
} = require('./countryValidationMiddleware');
const {
	addBusinessUnitMiddleware,
	addDepartmentMiddleware,
	addDesignationMiddleware,
	updateBusinessUnitMiddleware,
	updateDepartmentMiddleware,
	updateDesignationMiddleware,
} = require('./company-details.middleware');
const clientIpMiddleware = require('./clientIpMiddleware');
const {
	createCompanyMiddleware,
} = require('./glorified-client-admin.middleware');
const {
	createModuleAdminMiddleware,
	deleteModuleAdminMiddleware,
} = require('./hr-module.middleware');
const {
	getEditRequestsMiddleware,
	addEditRequestMiddleware,
	updateEditRequestMiddleware,
	updateEditRequestStatusMiddleware,
	deleteEditRequestMiddleware,
} = require('./editRequest.middleware');
const rateLimiter = require('./rateLimiterMiddleware');
const {
	updateShiftSettingMiddleware,
	createShiftSettingMiddleware,
	assignShiftsMiddleware,
} = require('./shiftSettingMiddleware');
const {
	createProjectMiddleware,
	switchProjectLeadMiddleware,
	updateProjectMiddleware,
	createTaskMiddleware,
	deleteProjectMiddleware,
	getSingleProjectMiddleware,
} = require('./projectsAndTasksMiddleware');

module.exports = {
	notFoundMiddleware,
	errorHandlingMiddleware,
	authenticationMiddleware,
	authorizePermissions,
	loginMiddleware,
	mobileLoginMiddleware,
	registerEmailMiddleware,
	verifyEmailMiddleware,
	registerMiddleware,
	updatePasswordMiddleware,
	uploadImage,
	uploadImageAndPdf,
	uploadExcelFile,
	validateEmail,
	onboardingStepOneMiddleware,
	employeePersonalDetailsMiddleware,
	onboardingFinalStepMiddleware,
	validateCountryId,
	validateCountryData,
	validateDebouncedInputCities,
	forgotPasswordMiddleware,
	addBusinessUnitMiddleware,
	addDepartmentMiddleware,
	addDesignationMiddleware,
	updateBusinessUnitMiddleware,
	updateDepartmentMiddleware,
	updateDesignationMiddleware,
	clientIpMiddleware,
	createCompanyMiddleware,
	createModuleAdminMiddleware,
	deleteModuleAdminMiddleware,
	getEditRequestsMiddleware,
	addEditRequestMiddleware,
	updateEditRequestMiddleware,
	updateEditRequestStatusMiddleware,
	deleteEditRequestMiddleware,
	rateLimiter,
	qrMiddleware,
	createShiftSettingMiddleware,
	assignShiftsMiddleware,
	updateShiftSettingMiddleware,
	createProjectMiddleware,
	switchProjectLeadMiddleware,
	updateProjectMiddleware,
	createTaskMiddleware,
	deleteProjectMiddleware,
	getSingleProjectMiddleware,
};
