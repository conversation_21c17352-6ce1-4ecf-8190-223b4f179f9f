const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

const ExperienceDetailsSchema = new mongoose.Schema(
	{
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
		location: {
			type: String,
			required: [true, 'Location is required'],
			trim: true,
		},
		companyName: {
			type: String,
			required: [true, 'Company Name is required'],
			trim: true,
		},
		designation: {
			type: String,
			required: [true, 'Designation is required'],
			trim: true,
		},
		periodFrom: {
			type: Date,
			required: [true, 'Start Date (Period From) is required'],
		},
		periodTo: {
			type: Date,
			required: [true, 'End Date (Period To) is required'],
			validate: {
				validator: function (value) {
					return this.periodFrom ? value > this.periodFrom : true;
				},
				message: 'End Date must be after Start Date',
			},
		},
		reasonForLeaving: {
			type: String,
			trim: true,
		},
		document: {
			type: String,
			trim: true,
		},
	},
	{ timestamps: true }
);

ExperienceDetailsSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
ExperienceDetailsSchema.plugin(aggregatePaginate);
ExperienceDetailsSchema.plugin(autoPopulate);

module.exports = mongoose.model('ExperienceDetails', ExperienceDetailsSchema);
