const multer = require('multer');
const { z } = require('zod');
const { BadRequestError } = require('../errors');

const maxImageFileSize = 5 * 1024 * 1024; // 5MB
const maxImageAndPdfFileSize = 5 * 1024 * 1024; // 5MB

const allowedMimeTypes = {
	image: ['image/jpeg', 'image/png', 'image/webp'],
	video: ['video/mp4', 'video/webm'],
	raw: [
		'application/pdf',
		'application/msword',
		'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
		'application/vnd.ms-excel',
		'text/plain',
	],
};

const imageMediaSchema = z
	.object({
		mimetype: z
			.string()
			.refine((type) => allowedMimeTypes.image.includes(type), {
				message: 'Invalid file type. Allowed: JPEG, PNG, MP4, PDF',
			}),
		size: z.number().max(maxImageFileSize, 'File size must be under 5MB'),
	})
	.strict();
const imageAndPdfMediaSchema = z
	.object({
		mimetype: z.string().refine((type) => allowedMimeTypes.raw.includes(type), {
			message: 'Invalid file type. Allowed: JPEG, PNG, MP4, PDF',
		}),
		size: z.number().max(maxImageAndPdfFileSize, 'File size must be under 5MB'),
	})
	.strict();

const storage = multer.diskStorage({
	destination: (req, file, cb) => {
		cb(null, './public/temp');
	},
	filename: (req, file, cb) => {
		cb(null, file.originalname);
	},
});

const fileFilter = (req, file, cb) => {
	const { mimetype } = file;
	const allAllowed = [
		...allowedMimeTypes.image,
		...allowedMimeTypes.video,
		...allowedMimeTypes.raw,
	];

	if (allAllowed.includes(mimetype)) {
		cb(null, true);
	} else {
		cb(new Error('Unsupported file type'), false);
	}
};

const uploadImage = multer({
	storage,
	fileFilter,
	limits: { fileSize: maxImageFileSize },
});

const uploadImageAndPdf = multer({
	storage,
	fileFilter,
	limits: { fileSize: maxImageAndPdfFileSize },
});

const excelFilter = (req, file, cb) => {
	const fileTypes = /xlsx|xls/;
	const extname = fileTypes.test(path.extname(file.originalname).toLowerCase());
	const mimetype = file.mimetype.includes('spreadsheetml');

	if (mimetype && extname) {
		return cb(null, true);
	} else {
		return cb(new BadRequestError('Only Excel files are allowed!'));
	}
};

uploadExcelFile = multer({
	storage,
	fileFilter: excelFilter,
	limits: { fileSize: 10 * 1024 * 1024 }, // 5MB
});

const uploadHandler = multer({
	storage,
	fileFilter,
	limits: { fileSize: 500 * 1024 * 1024 }, // 500MB max
});

module.exports = {
	uploadImage,
	imageMediaSchema,
	imageAndPdfMediaSchema,
	uploadImageAndPdf,
	uploadExcelFile,
	uploadHandler,
};
