const express = require('express');
const router = express.Router();

// Middlewares
const {
	authenticationMiddleware,
	authorizePermissions,
} = require('../../middlewares');

// Models
const { userRoles } = require('../../models/user.model');

// Controllers
const {
	getAllClients,
	promoteClientToGlorifiedClient,
} = require('../../controllers/super-admin-controllers/promote-client.controller');

/**
 * @route   GET /api/v1/super-admin/clients
 * @desc    Get a list of all clients
 * @access  Private (SUPER_ADMIN)
 */
router
	.route('/clients')
	.get(
		authenticationMiddleware,
		authorizePermissions(userRoles.SUPER_ADMIN),
		getAllClients
	);

/**
 * @route   POST /api/v1/super-admin/promote-client
 * @desc    Promote a client to a glorified client
 * @access  Private (SUPER_ADMIN)
 */
router
	.route('/promote-client')
	.post(
		authenticationMiddleware,
		authorizePermissions(userRoles.SUPER_ADMIN),
		promoteClientToGlorifiedClient
	);

module.exports = router;
