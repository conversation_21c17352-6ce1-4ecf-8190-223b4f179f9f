const crypto = require('crypto');
const { sessionMap, socketTokenMap } = require('./sessionMap');

module.exports = (authNamespace) => {
	authNamespace.on('connection', (socket) => {
		console.log(`New user connected in auth namespace: ${socket.id}`);

		let interval;
		const sendNewToken = () => {
			const token = crypto.randomBytes(40).toString('hex');
			const oldToken = socketTokenMap.get(socket.id);
			if (oldToken) {
				sessionMap.delete(oldToken);
			}
			sessionMap.set(token, socket.id);
			socketTokenMap.set(socket.id, token);
			socket.emit('qr-session', { qrSessionId: token });
		};
		sendNewToken();
		interval = setInterval(sendNewToken, 30 * 1000);
		socket.on('disconnect', () => {
			const oldToken = socketTokenMap.get(socket.id);
			if (oldToken) sessionMap.delete(oldToken);
			socketTokenMap.delete(socket.id);
			clearInterval(interval);
		});
	});
};
