const { passwordPattern } = require('./constants');
const {
	attachCookiesToResponse,
	verifyToken,
	decryptToken,
	isMobileRequest,
	generateToken,
	getOrCreateRefreshToken,
	createTokenUser,
	createToken,
	encryptToken,
} = require('./token');
const sendEmail = require('./sendEmail');
const sendVerificationEmail = require('./sendVerificationEmail');
const { APIResponse } = require('./APIResponse');
const { uploadFileToCloudinary } = require('./cloudinary');
const requiredFieldsErrorFunction = require('./requiredFieldsErrorFunction');
const { calculateAge } = require('./dateRelatedFunctions');
const { logger } = require('./logger');
const sendResetPasswordEmail = require('./sendResetPasswordEmail');
const { convertToObjectId } = require('./misc');
const { sendOnboardingLinkEmail } = require('./sendOnboardingLinkEmail');
const {
	sendEmployeePasswordGenerationLinkEmail,
} = require('./sendEmployeePasswordGenerationLinkEmail');
const { checkPermissions } = require('./checkPermissions');
const { bulkImportEmployeesExcel } = require('./parseXlsx');
const createHash = require('./createHash');
const {
	getCustomTime,
	getCurrentTime,
	getTimeLogForToday,
	calculateDuration,
} = require('./attendanceTimeCalculation');

module.exports = {
	sendEmail,
	attachCookiesToResponse,
	verifyToken,
	createTokenUser,
	createToken,
	encryptToken,
	passwordPattern,
	sendVerificationEmail,
	APIResponse,
	uploadFileToCloudinary,
	decryptToken,
	requiredFieldsErrorFunction,
	calculateAge,
	logger,
	sendResetPasswordEmail,
	convertToObjectId,
	sendOnboardingLinkEmail,
	sendEmployeePasswordGenerationLinkEmail,
	isMobileRequest,
	generateToken,
	checkPermissions,
	bulkImportEmployeesExcel,
	createHash,
	getCurrentTime,
	getCustomTime,
	getTimeLogForToday,
	calculateDuration,
	getOrCreateRefreshToken,
};
