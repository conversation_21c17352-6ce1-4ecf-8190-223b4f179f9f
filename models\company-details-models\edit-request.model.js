const { Schema, model } = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const editRequestStatuses = {
	PENDING: 'pending',
	APPROVED: 'approved',
	REJECTED: 'rejected',
};

const editRequestSections = {
	PERSONAL_DETAILS: 'personal-details',
	FAMILY_DETAILS: 'family-details',
	EDUCATION_DETAILS: 'education-details',
	EXPERIENCE_DETAILS: 'experience-details',
	CONTACT_DETAILS: 'contact-details',
	EMPLOYMENT_DETAILS: 'employment-details',
	EARNINGS_DETAILS: 'earnings-details',
	BENEFITS: 'benefits',
	SKILLS: 'skills',
	EQUIPMENT: 'equipment',
	COMPANY_DETAILS: 'company-details',
};

const EditRequestSchema = new Schema({
	userId: {
		type: Schema.Types.ObjectId,
		ref: 'User',
		required: true,
	},
	section: {
		type: String,
		enum: Object.values(editRequestSections),
		required: true,
	},
	oldData: {
		type: Schema.Types.Mixed,
		required: true,
	},
	newData: {
		type: Schema.Types.Mixed,
		required: true,
	},
	reason: {
		type: String,
		required: false,
	},
	status: {
		type: String,
		enum: Object.values(editRequestStatuses),
		default: 'pending',
	},
	submittedAt: {
		type: Date,
		default: Date.now(),
	},
	approvedAt: {
		type: Date,
	},
	approvedBy: {
		type: Schema.Types.ObjectId,
		ref: 'User',
	},
	rejectedAt: {
		type: Date,
	},
	rejectedBy: {
		type: Schema.Types.ObjectId,
		ref: 'User',
	},
	rejectionReason: {
		type: String,
	},
});

EditRequestSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
EditRequestSchema.plugin(aggregatePaginate);
const EditRequest = model('EditRequest', EditRequestSchema);
module.exports = {
	EditRequest,
	editRequestStatuses,
	editRequestSections,
};
