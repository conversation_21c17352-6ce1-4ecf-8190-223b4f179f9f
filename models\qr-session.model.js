const mongoose = require('mongoose');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');
const mongooseDelete = require('mongoose-delete');

const QrSessionSchema = new mongoose.Schema(
	{
		qrSessionId: { type: String, required: true, unique: true, index: true },
		socketId: {
			type: String,
			// required: true, // will enable it once we start storing the session ids in db rather than the memory
			// unique: true,
			index: true,
		},
		status: {
			type: String,
			enum: ['pending', 'confirmed'],
			default: 'pending',
		},
		user: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
			default: null,
		},
		confirmedAt: { type: Date, default: null },
	},
	{ timestamps: true }
);

QrSessionSchema.plugin(autoPopulate);
QrSessionSchema.plugin(aggregatePaginate);
QrSessionSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});

module.exports = mongoose.model('QrSession', QrSessionSchema);
