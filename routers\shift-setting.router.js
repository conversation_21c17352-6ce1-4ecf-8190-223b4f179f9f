const express = require('express');
const {
	createShiftSettingMiddleware,
	assignShiftsMiddleware,
	updateShiftSettingMiddleware,
} = require('../middlewares');
const {
	createShift,
	getAllShifts,
	assignShifts,
	getEmployeesForShiftAssignment,
	getSingleShiftDetails,
	updateShift,
	getRosterShifts,
	getGenericShifts,
} = require('../controllers/attendance-module/shifts.controller');
const {
	authenticationMiddleware,
	authorizePermissions,
} = require('../middlewares');
const { userRoles } = require('../models/user.model');
const router = express.Router();

// the base route for this router is /shifts

/**
 * @route   GET /
 * @desc    Retrieve all shift settings
 * @access  Private (CLIENT_ADMIN, MODULE_ADMIN)
 */
router
	.route('/')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN
		),
		getAllShifts
	)

	/**
	 * @route   POST /
	 * @desc    Create a new shift setting
	 * @access  Private (CLIENT_ADMIN, MODULE_ADMIN)
	 */
	.post(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN
		),
		createShiftSettingMiddleware,
		createShift
	)

	/**
	 * @route   PATCH /
	 * @desc    Update a shift setting
	 * @access  Private (CLIENT_ADMIN, MODULE_ADMIN)
	 */

	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN
		),
		updateShiftSettingMiddleware,
		updateShift
	);

/**
 * @route   POST /assign-shift
 * @desc    Assign shifts to employees
 * @access  Private (CLIENT_ADMIN, MODULE_ADMIN)
 */
router
	.route('/assign-shift')
	.post(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN
		),
		assignShiftsMiddleware,
		assignShifts
	);

/**
 * @route   GET /assign-shift/employees
 * @desc    Get employees available for shift assignment
 * @access  Private (CLIENT_ADMIN, MODULE_ADMIN)
 */
router
	.route('/assign-shift/employees')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN
		),
		getEmployeesForShiftAssignment
	);

router
	.route('/generic-shifts')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN
		),
		getGenericShifts
	);
router
	.route('/roster-shifts')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN
		),
		getRosterShifts
	);

router
	.route('/:shiftId')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.MODULE_ADMIN
		),
		getSingleShiftDetails
	);

module.exports = router;
