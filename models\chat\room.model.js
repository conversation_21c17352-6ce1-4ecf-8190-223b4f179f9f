const mongoose = require('mongoose');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');
const mongooseDelete = require('mongoose-delete');

const RoomSchema = new mongoose.Schema(
	{
		name: { type: String, required: true },
		description: { type: String, default: '' },
		members: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }], // Users in the group
		admins: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }], // Admins of the group
		createdBy: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
			required: true,
		},
	},
	{ timestamps: true }
);

RoomSchema.plugin(autoPopulate);
RoomSchema.plugin(aggregatePaginate);
RoomSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});

module.exports = mongoose.model('Room', RoomSchema);
