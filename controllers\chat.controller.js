const { StatusCodes } = require('http-status-codes');
const { Chat, Room } = require('../models');
const { APIResponse, convertToObjectId } = require('../utils');

const getUserChats = async (req, res) => {
	const chats = await Chat.aggregate([
		{
			$match: {
				participants: convertToObjectId(req.user.userId),
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				let: { participantIds: '$participants' },
				pipeline: [
					{
						$match: {
							$expr: {
								$in: ['$userId', '$$participantIds'],
							},
						},
					},
					{
						$project: {
							_id: 0,
							userId: 1,
							name: '$nameOnNRIC',
							profilePhoto: 1,
						},
					},
				],
				as: 'participants',
			},
		},
		{
			$lookup: {
				from: 'messages',
				localField: 'lastMessage',
				foreignField: '_id',
				as: 'lastMessage',
			},
		},
		{
			$addFields: {
				lastMessage: { $first: '$lastMessage' },
			},
		},
	]);

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'success', chats));
};

const createNewChat = async (req, res) => {
	const { users, isGroup } = req.body;

	let chat;
	if (isGroup) {
		const room = await Room.create({
			...req.body,
			members: [...users, req.user.userId],
		});
		chat = await Chat.create({
			isGroup: true,
			room: room._id,
		});
		res
			.status(StatusCodes.CREATED)
			.json(new APIResponse(StatusCodes.CREATED, 'success', chat));
		return;
	}

	const existingChat = await Chat.findOne({
		participants: { $all: [req.user.userId, ...users] },
		room: null,
	});

	if (existingChat) {
		res
			.status(StatusCodes.OK)
			.json(new APIResponse(StatusCodes.OK, 'success', existingChat));
		return;
	}

	chat = await Chat.create({
		participants: [...users, req.user.userId],
	});

	res
		.status(StatusCodes.CREATED)
		.json(new APIResponse(StatusCodes.CREATED, 'success', chat));
};

module.exports = {
	getUserChats,
	createNewChat,
};
