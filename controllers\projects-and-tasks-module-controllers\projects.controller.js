const { StatusCodes } = require('http-status-codes');
const { EmploymentDetails, Project } = require('../../models');
const { convertToObjectId } = require('../../utils/misc');
const { APIResponse } = require('../../utils');
const { NotFoundError } = require('../../errors');
const { userRoles, User } = require('../../models/user.model');

const getEmployeesForProjects = async (req, res) => {
	const employees = await EmploymentDetails.aggregate([
		{
			$match: {
				reportingTo: convertToObjectId(req.user.userId),
			},
		},
		{
			$lookup: {
				from: 'users',
				localField: 'userId',
				foreignField: '_id',
				as: 'reporting',
			},
		},
		{
			$addFields: {
				reporting: { $first: '$reporting' },
			},
		},
		{
			$project: {
				_id: 0,
				reportingUserId: '$reporting._id',
				reportingUserName: '$reporting.name',
				profilePhoto: '$reporting.profilePhoto',
				email: '$reporting.email',
			},
		},
	]);

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'success', employees));
};

const createProject = async (req, res) => {
	const employee = await User.findOne({
		_id: req.body.lead,
		companyId: req.user.companyId._id,
	});
	if (!employee) {
		throw new NotFoundError('Lead not found');
	}
	const project = await Project.create({
		companyId: req.user.companyId._id,
		createdBy: req.user.userId,
		...req.body,
	});

	const assignedEmployees = await User.updateMany(
		{
			_id: { $in: req.body.employees },
			companyId: req.user.companyId._id,
		},
		{
			$addToSet: {
				projectsAssigned: project._id,
			},
		}
	);

	const assignedLead = await User.findOneAndUpdate(
		{
			_id: req.body.lead,
			companyId: req.user.companyId._id,
		},
		{
			$addToSet: { projectsAssigned: project._id },
		}
	);
	assignedLead.role =
		assignedLead.role < userRoles.PROJECT_ADMIN
			? assignedLead.role
			: userRoles.PROJECT_ADMIN;
	await assignedLead.save();
	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'Project Created Successfully', {
			project,
			assignedEmployees,
			assignedLead,
		})
	);
};

const switchProjectLead = async (req, res) => {
	const project = await Project.findOne({ _id: req.body.projectId });
	if (!project) {
		throw new NotFoundError('Project Not Found');
	}
	const existingLead = await User.findOne({ _id: project.lead._id });
	if (!existingLead) {
		throw new NotFoundError('Existing Lead not found');
	}
	existingLead.role =
		existingLead.role < userRoles.PROJECT_ADMIN
			? existingLead.role
			: userRoles.EMPLOYEE;

	// FIXME: Need to remove the project from the lead only if we want to remove that emp from the project
	// existingLead.projectsAssigned = existingLead.projectsAssigned.filter(
	// 	(projectId) => projectId.toString() !== req.body.projectId
	// );
	await existingLead.save();

	const newLead = await User.findOneAndUpdate(
		{
			_id: req.body.leadId,
			companyId: req.user.companyId._id,
		},
		{
			$addToSet: { projectsAssigned: project._id },
		}
	);
	if (!newLead) {
		throw new NotFoundError('Lead not found');
	}
	newLead.role =
		newLead.role < userRoles.PROJECT_ADMIN
			? newLead.role
			: userRoles.PROJECT_ADMIN;
	await newLead.save();
	project.lead = newLead._id;
	await project.save();
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Assigned Successfully'));
};

const deleteProject = async (req, res) => {
	const project = await Project.findOneAndDelete({ _id: req.params.projectId });
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Deleted Successfully'));
};

const getProjects = async (req, res) => {
	const projects = await Project.find({ companyId: req.user.companyId._id });
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Projects Fetched', projects));
};

const updateProject = async (req, res) => {
	const project = await Project.findOne({ _id: req.body.projectId });
	if (!project) {
		throw new NotFoundError('Project Not Found');
	}

	await Project.updateOne({ _id: req.body.projectId }, { $set: req.body });
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Updated Successfully'));
};

const getSingleProject = async (req, res) => {
	// const project = await Project.findOne({ _id: req.params.projectId });
	const project = await Project.aggregate([
		{
			$match: {
				_id: convertToObjectId(req.params.projectId),
			},
		},
		{
			$facet: {
				assignedEmployees: [
					{
						$lookup: {
							from: 'users',
							localField: '_id',
							foreignField: 'projectsAssigned',
							as: 'assigned',
						},
					},
					{
						$unwind: {
							path: '$assigned',
							includeArrayIndex: 'string',
							preserveNullAndEmptyArrays: false,
						},
					},
					{
						$project: {
							_id: 0,
							userId: '$assigned._id',
							name: '$assigned.name',
							profilePhoto: '$assigned.profilePhoto',
							email: '$assigned.email',
						},
					},
				],
			},
		},
	]);
	if (!project) {
		throw new NotFoundError('Project Not Found');
	}
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Success', project));
};

const assignProjectToEmployees = async (req, res) => {
	const employees = await User.updateMany(
		{
			_id: { $in: req.body.employeeIds },
			companyId: req.user.companyId._id,
		},
		{
			$addToSet: { projectsAssigned: req.body.projectId },
		}
	);

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Project Assigned'));
};

const removeEmployeesFromProject = async (req, res) => {
	const employees = await User.updateMany(
		{
			_id: { $in: req.body.employeeIds },
			companyId: req.user.companyId._id,
		},
		{
			$pull: { projectsAssigned: req.body.projectId },
		}
	);

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Employees Removed'));
};

module.exports = {
	getEmployeesForProjects,
	createProject,
	switchProjectLead,
	deleteProject,
	getProjects,
	updateProject,
	getSingleProject,
	assignProjectToEmployees,
	removeEmployeesFromProject,
};
