const z = require('zod');
const {
	editRequestStatuses,
	editRequestSections,
} = require('../models/company-details-models/edit-request.model');

const getEditRequestsSchema = z.object({
	userId: z.string().optional(),
	status: z.enum(Object.values(editRequestStatuses)).optional(),
	section: z.enum(Object.values(editRequestSections)).optional(),
});

const addEditRequestsSchema = z.object({
	oldData: z.object({}),
	newData: z.object({}),
	section: z.enum(Object.values(editRequestSections), {
		errorMap: () => ({ message: 'Valid section is required' }),
	}),
});

const updateEditRequestSchema = z.object({
	id: z.string().nonempty('Edit request ID is required'),
	oldData: z.object({}).optional(),
	newData: z.object({}).optional(),
	section: z
		.enum(Object.values(editRequestSections), {
			errorMap: () => ({ message: 'Valid section is required' }),
		})
		.optional(),
});

const updateEditRequestStatusSchema = z
	.object({
		id: z.string().nonempty('Edit request ID is required'),
		status: z.enum(
			[editRequestStatuses.APPROVED, editRequestStatuses.REJECTED],
			{
				errorMap: () => ({
					message: 'Status must be either approved or rejected',
				}),
			}
		),
		rejectionReason: z.string().optional(),
	})
	.superRefine((data, ctx) => {
		if (data.status === editRequestStatuses.REJECTED && !data.rejectionReason) {
			ctx.addIssue({
				path: ['rejectionReason'],
				code: z.ZodIssueCode.custom,
				message: 'Rejection reason is required when rejecting an edit request',
			});
		}
	});

const deleteEditRequestsSchema = z.object({
	ids: z
		.array(z.string().nonempty('Edit request ID is required'))
		.min(1, 'At least one edit request ID is required'),
});

module.exports = {
	getEditRequestsSchema,
	addEditRequestsSchema,
	updateEditRequestSchema,
	updateEditRequestStatusSchema,
	deleteEditRequestsSchema,
};
