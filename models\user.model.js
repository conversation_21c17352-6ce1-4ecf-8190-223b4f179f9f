const mongoose = require('mongoose');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');
const mongooseDelete = require('mongoose-delete');
const bcrypt = require('bcryptjs');
const validator = require('validator');
const { Schema } = require('mongoose');

const userRoles = {
	SUPER_ADMIN: 1,
	PARTNER_ADMIN: 2,
	GLORIFIED_CLIENT_ADMIN: 3,
	CLIENT_ADMIN: 4,
	BUSINESS_ADMIN: 5,
	DEPARTMENT_ADMIN: 6,
	MODULE_ADMIN: 7,
	// PREV : this was the old structure
	// MODULE_ADMIN: {
	// 	HR_MODULE: 7.1,
	// 	LEAVE_MODULE: 7.2,
	// 	ATTENDANCE_MODULE: 7.3,
	// 	EXPENSE_MODULE: 7.4,
	// 	PAYROLL_MODULE: 7.5,
	// 	PROJECT_MODULE: 7.6,
	// 	COMMUNICATION_MODULE: 7.7,
	// 	PERFORMANCE_MODULE: 7.8,
	// 	SYSTEM_SETTINGS_MODULE: 7.9,
	// },
	PROJECT_ADMIN: 9,
	EMPLOYEE: 10,
	COUNTRY_ADMIN: 11,
};

const modules = {
	HR: 'hr',
	COMMUNICATION: 'communication',
	ATTENDANCE: 'attendance',
	LEAVE: 'leave',
	EXPENSE_CLAIM: 'expense_claim',
	PAYROLL: 'payroll',
	PROJECT_AND_TASKS: 'project_and_tasks',
	PERFORMANCE_AND_APPRAISALS: 'performance_and_appraisals',
	SYSTEM_SETTINGS: 'system_settings',
};

const UserSchema = new mongoose.Schema(
	{
		email: {
			type: String,
			required: [true, 'please provide email'],
			lowercase: true,
			unique: [
				true,
				'Oops! It looks like this email address is already registered. Please use a different email or log in to your account.',
			],
			validate: {
				validator: validator.isEmail,
				message: 'Please provide valid email',
			},
		},
		password: {
			type: String,
			minLength: [8, 'Password must be at least 8 characters long'],
			// select: false, // Ensures password is never sent in queries
			// we have disabled select because it was preventing the password from being sent in the comparePassword method
		},
		name: {
			type: String,
			index: true,
			trim: true,
		},
		profilePhoto: {
			type: String,
		},
		clientAdminId: {
			type: Schema.Types.ObjectId,
			ref: 'User',
		},
		role: {
			type: Number,
			enum: Object.values(userRoles),
			default: userRoles.EMPLOYEE,
		},
		moduleAdminAccess: [
			{
				// Module access for module admins
				type: String,
				enum: Object.values(modules),
			},
		],
		moduleAccess: {
			// Module access for glorified client admins
			type: Array,
			default: [],
		},
		companyId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'CompanyDetails',
			autopopulate: {
				select: 'businessName address logo',
			},
			// required: [true, "Please provide company id"],
		},

		verificationToken: {
			type: String,
		},
		verificationTokenExpiry: {
			type: Date,
		},
		isVerified: {
			type: Boolean,
			default: false,
		},
		verified: {
			type: Date,
		},
		resetPasswordToken: {
			type: String,
		},
		resetPasswordTokenExpiry: {
			type: Date,
		},
		isOnboard: {
			type: Boolean,
			default: false,
		},
		isOnboardStepOneComplete: {
			type: Boolean,
			default: false,
		},
		isHired: {
			type: Boolean,
			default: false,
		},
		isBlocked: {
			type: Boolean,
			default: false,
		},
		lastSeen: {
			type: Date,
		},
		status: {
			type: String, // this status is for chat
		},
		projectsAssigned: [
			{
				type: mongoose.Schema.Types.ObjectId,
				ref: 'Project',
			},
		],
	},
	{ timestamps: true }
);

UserSchema.pre('save', async function () {
	if (!this.isModified('password')) return;
	const salt = await bcrypt.genSalt(10);
	this.password = await bcrypt.hash(this.password, salt);
});

UserSchema.methods.comparePassword = async function (password) {
	const isPasswordCorrect = await bcrypt.compare(password, this.password);
	return isPasswordCorrect;
};

UserSchema.plugin(autoPopulate);
UserSchema.plugin(aggregatePaginate);
UserSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
const User = mongoose.model('User', UserSchema);
module.exports = {
	User,
	userRoles,
	modules,
};
