const express = require('express');
const router = express.Router();

// Controllers
const {
	getHolidays,
	addHoliday,
	updateHoliday,
	getHolidaysByHolidayGroupId,
	deleteHoliday,
} = require('../controllers/holiday.controller');

// Middlewares
const { authenticationMiddleware } = require('../middlewares');

const {
	addHolidayMiddleware,
	updateHolidayMiddleware,
	deleteHolidayMiddleware,
} = require('../middlewares/company-details.middleware');

/**
 * @route   GET /
 * @desc    Get all holidays
 * @access  Private (Authenticated)
 */
router.route('/').get(authenticationMiddleware, getHolidays);

/**
 * @route   POST /
 * @desc    Add a new holiday
 * @access  Private (Authenticated)
 */
router
	.route('/')
	.post(authenticationMiddleware, addHolidayMiddleware, addHoliday);

/**
 * @route   PATCH /
 * @desc    Update an existing holiday
 * @access  Private (Authenticated)
 */
router
	.route('/')
	.patch(authenticationMiddleware, updateHolidayMiddleware, updateHoliday);

/**
 * @route   GET /:holidayGroupId
 * @desc    Get holidays for a specific holiday group
 * @access  Private (Authenticated)
 */
router
	.route('/:holidayGroupId')
	.get(authenticationMiddleware, getHolidaysByHolidayGroupId);

/**
 * @route   PATCH /remove
 * @desc    Soft delete a holiday
 * @access  Private (Authenticated)
 */
router
	.route('/remove')
	.patch(authenticationMiddleware, deleteHolidayMiddleware, deleteHoliday);

module.exports = router;
