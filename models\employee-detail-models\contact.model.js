const mongoose = require('mongoose');

const ContactDetailsSchema = new mongoose.Schema(
	{
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
		type: {
			type: String,
			enum: ['reference', 'emergency'],
			required: [true, 'Please specify the contact type (reference/emergency)'],
		},
		name: {
			type: String,
			required: function () {
				return this.type === 'emergency';
			}, // Required for emergency contacts
			trim: true,
		},
		relationship: {
			type: String,
			required: [true, 'Please provide relationship'],
			trim: true,
		},
		phone: {
			type: String,
			required: [true, 'Please provide phone number'],
			match: [/^\d{8,15}$/, 'Phone number must be between 8 and 15 digits'],
		},
		countryDialCode: {
			type: String,
			required: [true, 'Please provide country dial code'],
			match: [
				/^\+\d{1,3}$/,
				'Country dial code must start with + and have between 1 and 3 digits',
			],
		},
		email: {
			type: String,
			required: function () {
				return this.type === 'reference';
			}, // Required for reference contacts
			trim: true,
			lowercase: true,
			match: [
				/^[\w.-]+@([\w-]+\.)+[\w-]{2,4}$/,
				'Please provide a valid email address',
			],
		},
	},
	{ timestamps: true }
);

module.exports = mongoose.model('ContactDetails', ContactDetailsSchema);
