const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

const ApprovalRequestSchema = new mongoose.Schema(
	{
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
		timeLogId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'TimeLog',
		},
		type: {
			type: String,
			enum: ['time-log', 'overtime'],
			required: true,
		},
		status: {
			type: String,
			enum: ['pending', 'approved', 'rejected'],
			default: 'pending',
		},
		updatedBy: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
		reasonForRejection: {
			type: String,
		},
	},
	{ timestamps: true }
);

ApprovalRequestSchema.plugin(autoPopulate);
ApprovalRequestSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
ApprovalRequestSchema.plugin(aggregatePaginate);

module.exports = mongoose.model('ApprovalRequest', ApprovalRequestSchema);
