const cloudinary = require('cloudinary').v2;
const fs = require('fs');
const path = require('path');
const { logger } = require('./logger');

cloudinary.config({
	cloud_name: process.env.CLOUD_NAME,
	api_key: process.env.CLOUD_API_KEY,
	api_secret: process.env.CLOUD_API_SECRET,
});

const uploadFileToCloudinary = async (localFilePath) => {
	const fileExtension = path.extname(localFilePath).toLowerCase();
	logger.info(`fileExtension: ${fileExtension}`);
	let resourceType = 'auto';
	if (
		['.pdf', '.docx', '.doc', '.xlsx', '.txt', '.csv'].includes(fileExtension)
	) {
		resourceType = 'raw';
	} else if (['.mp4', '.mov', '.webm'].includes(fileExtension)) {
		resourceType = 'video';
	}
	try {
		if (!localFilePath) return null;
		const response = await cloudinary.uploader.upload(localFilePath, {
			resource_type: resourceType,
			folder: process.env.CLOUD_FOLDER_NAME,
			overwrite: true,
		});
		fs.unlinkSync(localFilePath);
		return response.secure_url;
	} catch (error) {
		fs.unlinkSync(localFilePath);
		logger.error(error);
		return null;
	}
};

const uploadLargeFileToCloudinary = async (localFilePath) => {
	try {
		const result = await cloudinary.uploader.upload_large(localFilePath, {
			resource_type: 'video',
			chunk_size: 6 * 1024 * 1024, // 6MB
			folder: 'task-bug-media',
		});

		fs.unlinkSync(localFilePath); // Clean up after upload
		return result.secure_url;
	} catch (error) {
		fs.unlinkSync(localFilePath); // Clean up on error
		console.error('Upload error:', error);
		return null;
	}
};

module.exports = { uploadFileToCloudinary, uploadLargeFileToCloudinary };
