const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');
const { convertToObjectId } = require('../../utils/misc');
const { BadRequestError } = require('../../errors');

const DepartmentSchema = new mongoose.Schema({
	name: {
		type: String,
		required: true,
	},
	businessUnitId: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'BusinessUnit',
		required: true,
	},
	companyId: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'CompanyDetails',
		required: true,
	},
	admin: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'User',
	},
});

DepartmentSchema.pre(
	'updateMany',
	{ document: false, query: true },
	async function (next) {
		const filter = this.getFilter();
		const update = this.getUpdate();
		if (update['$set']?.deleted === true) {
			const departmentIds = filter._id.$in
				? filter._id.$in.map((id) => convertToObjectId(id))
				: [convertToObjectId(filter._id)];
			const hasDesignations = await mongoose
				.model('Designation')
				.exists({ departmentId: { $in: departmentIds }, deleted: false });
			if (hasDesignations) {
				return next(
					new BadRequestError(
						'Please transfer the designations of the department to other department first, then delete.'
					)
				);
			}
		}
		next();
	}
);

DepartmentSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
DepartmentSchema.plugin(aggregatePaginate);
DepartmentSchema.plugin(autoPopulate);

module.exports = mongoose.model('Department', DepartmentSchema);
