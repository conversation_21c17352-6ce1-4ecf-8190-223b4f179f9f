const { StatusCodes } = require('http-status-codes');
const {
	NotFoundError,
	BadRequestError,
	UnauthorizedError,
} = require('../errors');
const {
	EditRequest,
	editRequestSections,
} = require('../models/company-details-models/edit-request.model');
const {
	PersonalDetails,
	FamilyDetails,
	EducationDetails,
	ExperienceDetails,
	ContactDetails,
	EmploymentDetails,
	Earnings,
	Benefits,
	Skills,
	EquipmentDetails,
} = require('../models');
const { APIResponse, convertToObjectId } = require('../utils');
const {
	getEditRequestsAggregation,
} = require('../db/aggregations/edit-requests.aggregation');
const { filterOldData, deepEqual } = require('../utils/compareData');

const getEditRequests = async (req, res) => {
	const { userId, status, section } = req.query;
	const page = parseInt(req.query.page) || 1;
	const limit = parseInt(req.query.limit) || 10;

	const editRequestsAggregateQuery = EditRequest.aggregate(
		getEditRequestsAggregation({ userId, status, section })
	);

	const options = { page, limit };
	const result = await EditRequest.aggregatePaginate(
		editRequestsAggregateQuery,
		options
	);

	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'Success', {
			editRequests: result.docs,
			totalPages: result.totalPages,
			currentPage: result.page,
			totalResults: result.totalDocs,
		})
	);
};

const addEditRequest = async (req, res) => {
	const { oldData, newData, section, reason } = req.body;

	// Filter oldData to only include fields that are in newData
	const filteredOldData = filterOldData(oldData, newData);

	// Check if there are actual changes between filtered oldData and newData
	if (deepEqual(filteredOldData, newData)) {
		throw new BadRequestError('No changes detected between old and new data');
	}

	// Create new edit request
	const editRequest = await EditRequest.create({
		userId: req.user.userId,
		oldData: filteredOldData,
		newData,
		section,
		reason,
		submittedAt: new Date(),
	});

	res
		.status(StatusCodes.CREATED)
		.json(
			new APIResponse(
				StatusCodes.CREATED,
				'Edit request submitted successfully',
				editRequest
			)
		);
};

const updateEditRequest = async (req, res) => {
	const { id, oldData, newData, section, reason } = req.body;

	// Find the edit request
	const editRequest = await EditRequest.findOne({ _id: id, deleted: false });

	if (!editRequest) {
		throw new NotFoundError('Edit request not found');
	}

	// TODO: Check if user is authorized to update this edit request
	// if (
	// 	editRequest.userId.toString() !== req.user._id.toString() &&
	// 	req.user.role > 5
	// ) {
	// 	throw new UnauthorizedError(
	// 		'You are not authorized to update this edit request'
	// 	);
	// }

	// Check if edit request is already approved or rejected
	if (editRequest.status !== 'pending') {
		throw new BadRequestError(
			`Cannot update edit request with status: ${editRequest.status}`
		);
	}

	// Update the edit request
	const updateData = { reason };

	if (oldData && newData) {
		// Filter oldData to only include fields that are in newData
		const filteredOldData = filterOldData(oldData, newData);

		// Check if there are actual changes between filtered oldData and newData
		if (deepEqual(filteredOldData, newData)) {
			throw new BadRequestError('No changes detected between old and new data');
		}

		updateData.oldData = filteredOldData;
		updateData.newData = newData;
	} else {
		// If only one of oldData or newData is provided, use the existing logic
		if (oldData) updateData.oldData = oldData;
		if (newData) updateData.newData = newData;
	}

	const updatedEditRequest = await EditRequest.findByIdAndUpdate(
		id,
		updateData,
		{ new: true, runValidators: true }
	);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(
				StatusCodes.OK,
				'Edit request updated successfully',
				updatedEditRequest
			)
		);
};

const updateEditRequestStatus = async (req, res) => {
	const { id, status, rejectionReason } = req.body;

	// Find the edit request
	const editRequest = await EditRequest.findOne({
		_id: id,
		deleted: false,
	}).populate('userId', 'email');

	if (!editRequest) {
		throw new NotFoundError('Edit request not found');
	}

	// Check if edit request is already approved or rejected
	if (editRequest.status !== 'pending') {
		throw new BadRequestError(`Edit request is already ${editRequest.status}`);
	}

	// Update status and related fields
	const updateData = { status };

	if (status === 'approved') {
		updateData.approvedAt = new Date();
		updateData.approvedBy = req.user.userId;

		// Apply the changes based on the section
		await applyEditRequestChanges(editRequest);
	} else if (status === 'rejected') {
		updateData.rejectedAt = new Date();
		updateData.rejectedBy = req.user.userId;
		updateData.rejectionReason = rejectionReason;
	}

	const updatedEditRequest = await EditRequest.findByIdAndUpdate(
		id,
		updateData,
		{ new: true, runValidators: true }
	).populate('userId', 'email');

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(
				StatusCodes.OK,
				`Edit request ${status === 'approved' ? 'approved' : 'rejected'} successfully`,
				updatedEditRequest
			)
		);
};

const deleteEditRequests = async (req, res) => {
	const { ids } = req.body;

	// Find all edit requests
	const editRequests = await EditRequest.find({
		_id: { $in: ids },
		deleted: false,
	});

	if (editRequests.length === 0) {
		throw new NotFoundError('No edit requests found');
	}

	// Check if any request has non-pending status
	const nonPendingRequest = editRequests.find(
		(request) => request.status !== 'pending'
	);
	if (nonPendingRequest) {
		throw new BadRequestError(
			`Cannot delete edit request with status: ${nonPendingRequest.status}`
		);
	}

	// TODO: Check if user is authorized to delete these edit requests
	// const unauthorizedRequest = editRequests.find(request =>
	//   request.userId.toString() !== req.user._id.toString() && req.user.role > 5
	// );
	// if (unauthorizedRequest) {
	// 	throw new UnauthorizedError(
	// 		'You are not authorized to delete one or more edit requests'
	// 	);
	// }

	// Soft delete all edit requests
	await EditRequest.delete({ _id: { $in: ids } }, req.user.userId);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, 'Edit requests deleted successfully')
		);
};

const applyEditRequestChanges = async (editRequest) => {
	const { section, newData } = editRequest;
	const userId = editRequest.userId._id;

	switch (section) {
		case editRequestSections.PERSONAL_DETAILS:
			await PersonalDetails.findOneAndUpdate(
				{ userId },
				{ $set: newData },
				{ runValidators: true }
			);
			break;

		case editRequestSections.FAMILY_DETAILS:
			await FamilyDetails.findOneAndUpdate(
				{ userId },
				{ $set: newData },
				{ runValidators: true }
			);
			break;

		case editRequestSections.EDUCATION_DETAILS:
			// Handle education details which might include array operations
			if (Array.isArray(newData.education)) {
				const bulkOps = [];

				newData.education.forEach((education) => {
					if (education._id) {
						// Update existing education record
						bulkOps.push({
							updateOne: {
								filter: { _id: convertToObjectId(education._id) },
								update: { $set: education },
							},
						});
					} else {
						// Insert new education record
						bulkOps.push({
							insertOne: {
								document: {
									userId,
									...education,
								},
							},
						});
					}
				});

				if (bulkOps.length > 0) {
					await EducationDetails.bulkWrite(bulkOps);
				}
			}

			// Update skills if included
			if (newData.skills) {
				await Skills.findOneAndUpdate(
					{ userId },
					{
						$set: {
							hardSkills: newData.skills.hardSkills || [],
							softSkills: newData.skills.softSkills || [],
						},
					},
					{ upsert: true, runValidators: true }
				);
			}
			break;

		case editRequestSections.EXPERIENCE_DETAILS:
			// Handle experience details which might include array operations
			if (Array.isArray(newData.experience)) {
				const bulkOps = [];

				newData.experience.forEach((experience) => {
					if (experience._id) {
						// Update existing experience record
						bulkOps.push({
							updateOne: {
								filter: { _id: convertToObjectId(experience._id) },
								update: { $set: experience },
							},
						});
					} else {
						// Insert new experience record
						bulkOps.push({
							insertOne: {
								document: {
									userId,
									...experience,
								},
							},
						});
					}
				});

				if (bulkOps.length > 0) {
					await ExperienceDetails.bulkWrite(bulkOps);
				}
			}
			break;

		case editRequestSections.CONTACT_DETAILS:
			if (Array.isArray(newData.contact)) {
				const bulkOps = [];

				newData.contact.forEach((contact) => {
					if (contact._id) {
						// Update existing contact record
						bulkOps.push({
							updateOne: {
								filter: { _id: convertToObjectId(contact._id) },
								update: { $set: contact },
							},
						});
					} else {
						// Insert new contact record
						bulkOps.push({
							insertOne: {
								document: {
									userId,
									...contact,
								},
							},
						});
					}
				});

				if (bulkOps.length > 0) {
					await ContactDetails.bulkWrite(bulkOps);
				}
			}
			break;

		case editRequestSections.EMPLOYMENT_DETAILS:
			await EmploymentDetails.findOneAndUpdate(
				{ userId },
				{ $set: newData },
				{ runValidators: true }
			);
			break;

		case editRequestSections.EARNINGS_DETAILS:
			await Earnings.findOneAndUpdate(
				{ userId },
				{ $set: newData },
				{ runValidators: true }
			);
			break;

		case editRequestSections.BENEFITS:
			await Benefits.findOneAndUpdate(
				{ userId },
				{ $set: newData },
				{ runValidators: true }
			);
			break;

		case editRequestSections.EQUIPMENT:
			// Handle equipment which might include array operations
			if (Array.isArray(newData.equipment)) {
				const bulkOps = [];

				newData.equipment.forEach((equipment) => {
					if (equipment._id) {
						// Update existing equipment record
						bulkOps.push({
							updateOne: {
								filter: { _id: convertToObjectId(equipment._id) },
								update: { $set: equipment },
							},
						});
					} else {
						// Insert new equipment record
						bulkOps.push({
							insertOne: {
								document: {
									userId,
									...equipment,
								},
							},
						});
					}
				});

				if (bulkOps.length > 0) {
					await EquipmentDetails.bulkWrite(bulkOps);
				}
			}
			break;

		default:
			throw new BadRequestError(`Unsupported section: ${section}`);
	}
};

module.exports = {
	getEditRequests,
	addEditRequest,
	updateEditRequest,
	updateEditRequestStatus,
	deleteEditRequests,
};
