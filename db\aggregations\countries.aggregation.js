const { convertToObjectId } = require('../../utils');

const countriesAndCities = [
	{
		$unwind: {
			path: '$states',
		},
	},
	{
		$unwind: {
			path: '$states.cities',
		},
	},
	{
		$group: {
			_id: '$_id',
			currency_name: {
				$first: '$currency_name',
			},
			currency_symbol: {
				$first: '$currency_symbol',
			},
			currency: {
				$first: '$currency',
			},
			cities: {
				$push: '$states.cities.name',
			},
			country_code: {
				$first: '$iso3',
			},
			country_name: {
				$first: '$name',
			},
		},
	},
	{
		$sort: {
			country_name: 1,
		},
	},
];

const debouncedInputCities = ({ country, input }) => {
	return [
		{
			$unwind: '$states',
		},
		{
			$unwind: '$states.cities',
		},
		{
			$match: {
				_id: convertToObjectId(country),
			},
		},
		{
			$match: {
				'states.cities.name': {
					$regex: input,
					$options: 'i',
				},
			},
		},
		{
			$project: {
				city: '$states.cities.name',
				state: '$states.name',
				_id: 0,
			},
		},
	];
};

const allCountries = [
	{
		$project: {
			_id: 1,
			name: 1,
			iso2: 1,
			iso3: 1,
			currency: 1,
			currencyName: 1,
			currencySymbol: 1,
			phoneCode: 1,
		},
	},
	{
		$sort: {
			name: 1,
		},
	},
];

const allCities = (country) => {
	return [
		{
			$match: {
				id: country,
			},
		},
		{
			$unwind: '$states',
		},
		{
			$unwind: '$states.cities',
		},
		{
			$group: {
				_id: '$name',
				cities: {
					$push: '$states.cities.name',
				},
			},
		},
		{
			$project: {
				_id: 0,
				cities: 1,
			},
		},
	];
};

const allCountryCities = (countryName) => {
	return [
		{
			$match: {
				name: countryName,
			},
		},
		{
			$unwind: '$states',
		},
		{
			$unwind: '$states.cities',
		},
		{
			$project: {
				_id: '$states.cities._id',
				name: '$states.cities.name',
				latitude: '$states.cities.latitude',
				longitude: '$states.cities.longitude',
				state: '$states.name',
				stateCode: '$states.stateCode',
			},
		},
	];
};

const allCurrencies = [
	{
		$group: {
			_id: '$currencyName',
			name: { $first: '$name' },
			currency: { $first: '$currency' },
			currencyName: { $first: '$currencyName' },
			currencySymbol: { $first: '$currencySymbol' },
		},
	},
	{
		$sort: {
			currencyName: 1,
		},
	},
	{
		$project: {
			_id: 0,
			name: 1,
			currency: 1,
			currencyName: 1,
			currencySymbol: 1,
		},
	},
];

const allDialCodes = [
	{
		$group: {
			_id: null,
			phoneCodes: {
				$addToSet: {
					$concat: ['+', '$phoneCode'],
				},
			},
		},
	},
	{
		$project: {
			_id: 0,
			phoneCodes: 1,
		},
	},
];

module.exports = {
	countriesAndCities,
	debouncedInputCities,
	allCountryCities,
	allCountries,
	allCities,
	allCurrencies,
	allDialCodes,
};
