const { z } = require('zod');
const createShiftSettingSchema = z
	.object({
		type: z.enum(['generic', 'roster']).default('generic'),
		code: z.string().min(1, 'Attendance code is required'),
		name: z.string().min(1, 'Setting name is required'),

		isApprovalStatusEnabled: z.boolean().default(false),
		approvalFrequency: z.enum(['daily', 'weekly', 'monthly']).default('daily'),

		startTime: z
			.string()
			.regex(/^([0-1]\d|2[0-3]):([0-5]\d)$/, 'Invalid time format'),
		endTime: z
			.string()
			.regex(/^([0-1]\d|2[0-3]):([0-5]\d)$/, 'Invalid time format'),

		clockInLimit: z.number().int().nonnegative().default(1),
		clockOutLimit: z.number().int().nonnegative().default(1),
		breakLimit: z.number().int().nonnegative().default(3),
		clockInDelay: z.number().int().nonnegative().default(15),
		clockOutDelay: z.number().int().nonnegative().default(120),
		tardinessMinutes: z.number().int().nonnegative().default(0),
		tardinessMinutesPerMonth: z.number().int().nonnegative().default(0),
		workLocationCheckEnabled: z.boolean().default(false),
		weekStartsFrom: z
			.enum([
				'sunday',
				'monday',
				'tuesday',
				'wednesday',
				'thursday',
				'friday',
				'saturday',
			])
			.default('monday'),
		allowedAttendanceDays: z
			.array(
				z.enum([
					'monday',
					'tuesday',
					'wednesday',
					'thursday',
					'friday',
					'saturday',
					'sunday',
				])
			)
			.default(['monday', 'tuesday', 'wednesday', 'thursday', 'friday']),
		halfDayMark: z
			.string()
			// .regex(/^([0-1]\d|2[0-3]):([0-5]\d)$/)
			.optional(),
		maxLoginHours: z.number().optional(),
		minLoginHours: z.number().optional(),
		maxClockInTime: z.number().optional(),
		workLocation: z
			.array(
				z.object({
					latitude: z.number(),
					longitude: z.number(),
					radiusInMeters: z.number(),
				})
			)
			.optional(),
		wifiCheckEnabled: z.boolean().default(false),
		OfficeWifiIPAddress: z.array(z.string()).optional(),
		qrClockInCheckEnabled: z.boolean().default(false),
		facialCheckEnabled: z.boolean().default(false),
	})
	.strict();

const updateShiftSettingSchema = z
	.object({
		shiftId: z.string().regex(/^[a-f\d]{24}$/, 'Invalid shift ID'),
		code: z.string().min(1, 'Attendance code is required'),
		name: z.string().min(1, 'Setting name is required'),
		isApprovalStatusEnabled: z.boolean().default(false),
		approvalFrequency: z.enum(['daily', 'weekly', 'monthly']).default('daily'),
		startTime: z
			.string()
			.regex(/^([0-1]\d|2[0-3]):([0-5]\d)$/, 'Invalid time format'),
		endTime: z
			.string()
			.regex(/^([0-1]\d|2[0-3]):([0-5]\d)$/, 'Invalid time format'),
		clockInLimit: z.number().int().nonnegative().default(1),
		clockOutLimit: z.number().int().nonnegative().default(1),
		breakLimit: z.number().int().nonnegative().default(3),
		clockInDelay: z.number().int().nonnegative().default(15),
		clockOutDelay: z.number().int().nonnegative().default(120),
		tardinessMinutes: z.number().int().nonnegative().default(0),
		tardinessMinutesPerMonth: z.number().int().nonnegative().default(0),
		workLocationCheckEnabled: z.boolean().default(false),
		weekStartsFrom: z
			.enum([
				'sunday',
				'monday',
				'tuesday',
				'wednesday',
				'thursday',
				'friday',
				'saturday',
			])
			.default('monday'),
		allowedAttendanceDays: z
			.array(
				z.enum([
					'monday',
					'tuesday',
					'wednesday',
					'thursday',
					'friday',
					'saturday',
					'sunday',
				])
			)
			.default(['monday', 'tuesday', 'wednesday', 'thursday', 'friday']),
		halfDayMark: z
			.string()
			// .regex(/^([0-1]\d|2[0-3]):([0-5]\d)$/)
			.optional(),
		maxLoginHours: z.number().optional(),
		minLoginHours: z.number().optional(),
		maxClockInTime: z.number().optional(),
		workLocation: z
			.array(
				z.object({
					latitude: z.number(),
					longitude: z.number(),
					radiusInMeters: z.number(),
				})
			)
			.optional(),
		wifiCheckEnabled: z.boolean().default(false),
		OfficeWifiIPAddress: z.array(z.string()).optional(),
		qrClockInCheckEnabled: z.boolean().default(false),
		facialCheckEnabled: z.boolean().default(false),
	})
	.strict();

const assignShiftSchema = z
	.object({
		shiftId: z.string().regex(/^[a-f\d]{24}$/, 'Invalid shift ID'),
		employeeIds: z.array(
			z.string().regex(/^[a-f\d]{24}$/, 'Invalid employee ID')
		),
	})
	.strict();

module.exports = {
	assignShiftSchema,
	createShiftSettingSchema,
	updateShiftSettingSchema,
};
