const { z } = require('zod');

const onboardingStepOneSchema = z
	.object({
		businessName: z
			.string()
			.nonempty('Business name is required')
			.min(4, { message: 'Business name must be at least 4 characters long' }),
		businessCountry: z
			.string()
			.regex(/^[a-fA-F0-9]{24}$/, 'Invalid ObjectId')
			.nonempty('Country ID is required'),
		registration: z
			.string()
			.nonempty('Registration is required')
			.min(6, { message: 'Registration must be at least 6 characters long' })
			.regex(/^[a-zA-Z0-9]+$/, {
				message: 'Registration should only contain letters and numbers',
			}),
		address: z
			.string()
			.nonempty('Address is required')
			.min(2, { message: 'Address must be at least 2 characters long' }),
	})
	.strict();

const timeFormatEnum = ['12h', '24h'];
const dateFormatEnum = ['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD'];

const onboardingFinalStepSchema = z
	.object({
		currency: z
			.string()
			.nonempty('Currency is required')
			.min(2, { message: 'Currency must be at least 2 characters long' })
			.regex(/^[a-zA-Z\s]+$/, {
				message: 'Currency must contain only letters and spaces',
			}),

		timeFormat: z
			.string()
			.nonempty('Time Format is required')
			.min(2, { message: 'Time Format must be at least 2 characters long' })
			.refine(
				(format) => {
					return timeFormatEnum.includes(format);
				},
				{
					message: `Time Format must be one of the following: ${timeFormatEnum.join(
						', '
					)}`,
				}
			),

		dateFormat: z
			.string()
			.nonempty('Date Format is required')
			.min(2, { message: 'Date Format must be at least 10 characters long' })
			.refine((format) => dateFormatEnum.includes(format), {
				message: `Date Format must be one of the following: ${dateFormatEnum.join(
					', '
				)}`,
			}),

		branches: z.array(
			z.object({
				branchId: z.string().nonempty('Branch ID is required').optional(),
				branchName: z.string().nonempty('Branch name is required').min(2, {
					message: 'Branch name must be at least 2 characters long',
				}),
				branchLocation: z
					.string()
					.nonempty('Branch location is required')
					.min(2, {
						message: 'Branch location must be at least 2 characters long',
					}),
				departments: z.array(
					z.object({
						departmentName: z
							.string()
							.nonempty('Department name is required')
							.min(2, {
								message: 'Department name must be at least 2 characters long',
							}),
					})
				),
			})
		),
	})
	.strict();

module.exports = { onboardingStepOneSchema, onboardingFinalStepSchema };
