const cron = require('node-cron');
const {
	clockOutGenericShiftEmployee,
} = require('../services/clockOut.service');
const { logger } = require('../utils');

// This cron job runs at 1:00 AM every day
cron.schedule('0 1 * * *', async () => {
	console.log('clocking out employees who are not clocked out');
	logger.info('clocking out employees who are not clocked out');
	await clockOutGenericShiftEmployee();
});

module.exports = cron;
