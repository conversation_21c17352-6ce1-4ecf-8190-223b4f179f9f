const dayjs = require('dayjs');

const calculateAge = (dob) => {
	return dayjs().diff(dayjs(dob), year);
};

const calculatePrStatus = (issueDate) => {
	if (!issueDate || !dayjs(issueDate).isValid()) return 'invalid-date';

	const currentYear = dayjs().year();
	const issueYear = dayjs(issueDate).year();
	const yearsSinceIssue = currentYear - issueYear;

	if (yearsSinceIssue === 0 || yearsSinceIssue === 1) {
		return 'singapore-pr-year-1';
	} else if (yearsSinceIssue === 2) {
		return 'singapore-pr-year-2';
	} else {
		return 'singaporean / singapore-pr-year-3';
	}
};

module.exports = { calculateAge, calculatePrStatus };
