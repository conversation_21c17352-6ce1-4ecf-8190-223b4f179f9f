const dayjs = require('dayjs');
const { default: mongoose } = require('mongoose');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const mongooseDelete = require('mongoose-delete');
const autoPopulate = require('mongoose-autopopulate');

const HolidaySchema = new mongoose.Schema(
	{
		companyId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'CompanyDetails',
			required: [true, 'CompanyId is required in holiday'],
		},
		holidayGroupId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'HolidayGroup',
			required: [true, 'HolidayGroupId is required in holiday'],
		},
		title: {
			type: String,
			required: [true, 'Holiday title is required in holiday'],
		},
		startDate: {
			type: Date,
			required: [true, 'Holiday start date is required in holiday'],
		},
		endDate: {
			type: Date,
		},
		numberOfDays: {
			type: Number,
			required: true,
		},
		// TODO: refactor icon
		icon: {
			type: String,
			required: [true, 'Holiday icon is required in holiday'],
		},
	},
	{ timestamps: true }
);

HolidaySchema.plugin(aggregatePaginate);
HolidaySchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
HolidaySchema.plugin(autoPopulate);
module.exports = mongoose.model('Holiday', HolidaySchema);
