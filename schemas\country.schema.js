const { z } = require('zod');

const timezoneSchema = z.object({
	zoneName: z.string(),
	gmtOffset: z.number(),
	gmtOffsetName: z.string(),
	abbreviation: z.string(),
	tzName: z.string(),
});

const citySchema = z.object({
	id: z.number(),
	name: z.string(),
	latitude: z
		.number()
		.or(z.string().transform((val) => parseFloat(val)))
		.nullable(),
	longitude: z
		.number()
		.or(z.string().transform((val) => parseFloat(val)))
		.nullable(),
});

const stateSchema = z.object({
	id: z.number(),
	name: z.string(),
	state_code: z.string().nullable(),
	latitude: z
		.number()
		.or(z.string().transform((val) => parseFloat(val)))
		.nullable(),
	longitude: z
		.number()
		.or(z.string().transform((val) => parseFloat(val)))
		.nullable(),
	type: z.string().nullable(),
	cities: z.array(citySchema).default([]),
});

const countrySchema = z.object({
	id: z.number(),
	name: z.string().nonempty('Country name is required'),
	iso3: z.string().length(3),
	iso2: z.string().length(2),
	numeric_code: z.string().nullable(),
	phonecode: z.string().nullable(),
	capital: z.string().nullable(),
	currency: z.string().nullable(),
	currency_name: z.string().nullable(),
	currency_symbol: z.string().nullable(),
	tld: z.string().nullable(),
	native: z.string().nullable(),
	region: z.string().nullable(),
	region_id: z.number().nullable(),
	subregion: z.string().nullable(),
	subregion_id: z.number().nullable(),
	nationality: z.string().nullable(),
	timezones: z.array(timezoneSchema).default([]),
	translations: z.record(z.string()).nullable(),
	latitude: z
		.number()
		.or(z.string().transform((val) => parseFloat(val)))
		.nullable(),
	longitude: z
		.number()
		.or(z.string().transform((val) => parseFloat(val)))
		.nullable(),
	emoji: z.string().nullable(),
	emojiU: z.string().nullable(),
	states: z.array(stateSchema).default([]),
});

const debouncedInputCitiesSchema = z
	.object({
		citySearchTerm: z.string(),
		countryId: z.string().nonempty('Country name is required'),
	})
	.strict();

module.exports = { countrySchema, debouncedInputCitiesSchema };
