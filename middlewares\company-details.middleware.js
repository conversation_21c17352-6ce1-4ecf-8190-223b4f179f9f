const { BadRequestError } = require('../errors');
const {
	addBusinessUnitSchema,
	addDepartmentSchema,
	addDesignationSchema,
	updateBusinessUnitSchema,
	updateDepartmentSchema,
	updateDesignationSchema,
	deleteBusinessUnitSchema,
	deleteDepartmentSchema,
	deleteDesignationSchema,
	updateWorkParametersSchema,
	addHolidayGroupSchema,
	updateHolidayGroupSchema,
	deleteHolidayGroupSchema,
	addHolidaySchema,
	updateHolidaySchema,
	deleteHolidaysSchema,
	copyHolidayGroupSchema,
} = require('../schemas/company-details.schema');
const { areValidObjectIds } = require('../utils/misc');

const addBusinessUnitMiddleware = (req, res, next) => {
	const result = addBusinessUnitSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid Business Unit', result.error.format());
	}
	next();
};

const addDepartmentMiddleware = (req, res, next) => {
	const result = addDepartmentSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid Department', result.error.format());
	}
	next();
};

const addDesignationMiddleware = (req, res, next) => {
	const result = addDesignationSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid Designation', result.error.format());
	}
	next();
};

const updateBusinessUnitMiddleware = (req, res, next) => {
	const result = updateBusinessUnitSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid Business Unit', result.error.format());
	}
	next();
};

const updateDepartmentMiddleware = (req, res, next) => {
	const result = updateDepartmentSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid Department', result.error.format());
	}
	next();
};

const updateDesignationMiddleware = (req, res, next) => {
	const result = updateDesignationSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid Designation', result.error.format());
	}
	next();
};

const deleteBusinessUnitMiddleware = async (req, res, next) => {
	const isObjectIdsValid = areValidObjectIds(req.body.businessUnitIds);
	if (!isObjectIdsValid) {
		throw new BadRequestError('Invalid Branch Ids');
	}
	const result = deleteBusinessUnitSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError('Invalid Business Unit', result.error.format());
	}
	next();
};

const deleteDepartmentMiddleware = async (req, res, next) => {
	const isObjectIdsValid = areValidObjectIds(req.body.departmentIds);
	if (!isObjectIdsValid) {
		throw new BadRequestError('Invalid Department Ids');
	}
	const result = deleteDepartmentSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid Department', result.error.format());
	}
	next();
};

const deleteDesignationMiddleware = async (req, res, next) => {
	const isObjectIdsValid = areValidObjectIds(req.body.designationIds);
	if (!isObjectIdsValid) {
		throw new BadRequestError('Invalid Designation Ids');
	}
	const result = deleteDesignationSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError('Invalid Designation', result.error.format());
	}
	next();
};

const updateWorkParametersMiddleware = (req, res, next) => {
	const result = updateWorkParametersSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid Work Parameters', result.error.format());
	}
	next();
};

const addHolidayGroupMiddleware = (req, res, next) => {
	const result = addHolidayGroupSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid Holiday Group', result.error.format());
	}
	next();
};

const copyHolidayGroupMiddleware = (req, res, next) => {
	const result = copyHolidayGroupSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid Holiday Group', result.error.format());
	}
	next();
};

const updateHolidayGroupMiddleware = (req, res, next) => {
	const result = updateHolidayGroupSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid Holiday Group', result.error.format());
	}
	next();
};

const deleteHolidayGroupMiddleware = async (req, res, next) => {
	const isObjectIdsValid = areValidObjectIds(req.body.holidayGroupIds);
	if (!isObjectIdsValid) {
		throw new BadRequestError('Invalid Holiday Group Ids');
	}
	const result = deleteHolidayGroupSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError('Invalid Holiday Group', result.error.format());
	}
	next();
};

const addHolidayMiddleware = (req, res, next) => {
	const result = addHolidaySchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid Holiday Group', result.error.format());
	}
	next();
};

const updateHolidayMiddleware = (req, res, next) => {
	const result = updateHolidaySchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid Holiday Group', result.error.format());
	}
	next();
};

const deleteHolidayMiddleware = async (req, res, next) => {
	const isObjectIdsValid = areValidObjectIds(req.body.holidayIds);
	if (!isObjectIdsValid) {
		throw new BadRequestError('Invalid Holiday Ids');
	}
	const result = deleteHolidaysSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError('Invalid Holiday Group', result.error.format());
	}
	next();
};

module.exports = {
	addBusinessUnitMiddleware,
	addDepartmentMiddleware,
	addDesignationMiddleware,
	updateBusinessUnitMiddleware,
	updateDepartmentMiddleware,
	updateDesignationMiddleware,
	deleteBusinessUnitMiddleware,
	deleteDepartmentMiddleware,
	deleteDesignationMiddleware,
	updateWorkParametersMiddleware,
	addHolidayGroupMiddleware,
	copyHolidayGroupMiddleware,
	updateHolidayGroupMiddleware,
	deleteHolidayGroupMiddleware,
	addHolidayMiddleware,
	updateHolidayMiddleware,
	deleteHolidayMiddleware,
};
