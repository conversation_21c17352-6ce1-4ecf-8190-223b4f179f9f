const mongoose = require('mongoose');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const mongooseDelete = require('mongoose-delete');
const autoPopulate = require('mongoose-autopopulate');

const PersonalDetailsSchema = new mongoose.Schema(
	{
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
		employeeOrgId: {
			type: String,
		},
		nameOnNRIC: {
			type: String,
			required: [true, 'Please provide name as on NRIC ID'],
			index: true,
			trim: true,
		},
		countryDialCode: {
			type: String,
			required: [true, 'Please provide dial code'],
			trim: true,
		},
		mobile: {
			type: String,
			required: [true, 'Please provide mobile'],
			match: [/^\d{8,15}$/, 'Please provide a valid mobile number'], // 8-15 digits
			unique: true,
			index: true,
		},
		gender: {
			type: String,
			required: [true, 'Please provide gender'],
			enum: ['male', 'female', 'other'],
		},
		dateOfJoining: {
			type: Date,
			required: [true, 'Please provide date of joining'],
			index: true,
		},
		dob: {
			type: Date,
			required: [true, 'Please provide date of birth'],
		},
		age: {
			type: Number,
			required: true,
			min: [18, 'Employee must be at least 18 years old'],
		},
		nationality: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'Country',
			required: [true, 'Please provide nationality'],
		},
		residentialStatus: {
			type: String,
			required: true,
			enum: [
				'Singapore Citizen',
				'Singapore PR',
				'Employment Pass',
				'SPass',
				'Work Permit',
				'LOC',
			],
			default: function () {
				// return this.nationality.toLowerCase() === 'singapore'
				return this.nationality.toString() === '6800015d4161a479ac4c0640'
					? 'Singapore Citizen'
					: undefined;
			},
		},
		icFinNumber: {
			type: String,
			required: [true, 'Please provide IC/FIN number'],
			unique: true,
			trim: true,
			minLength: [9, 'IC/FIN number must be exactly 9 characters'],
			maxLength: [9, 'IC/FIN number must be exactly 9 characters'],
			match: [
				/^[STFGM][0-9]{7}[A-Z]$/,
				'IC/FIN number must be exactly 9 characters long with first and last character in upper case and remaining seven between the uppercase alphabets will be numerical',
			],
			index: true,
		},
		issueDate: {
			type: Date,
			// required: [true, 'Please provide issue date'],
		},
		expiryDate: {
			type: Date,
			// required: [true, 'Please provide expiry date'],
		},
		expiryDateReminder: {
			type: Date,
		},
		prStatus: {
			type: String,
			enum: [
				'singapore-pr-year-1',
				'singapore-pr-year-2',
				'singaporean / singapore-pr-year-3',
				'singaporean',
			],
			required: true,
		},

		religion: {
			type: String,
			required: [true, 'Please provide religion'],
			trim: true,
			lowercase: true,
		},
		race: {
			type: String,
			required: [true, 'Please provide race'],
			trim: true,
			lowercase: true,
		},
		country: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'Country',
			required: [true, 'Please provide country in address'],
		},
		postalCode: {
			type: String,
			required: [true, 'Please provide postal code'],
			trim: true,
			match: [/^\d{6}$/, 'Postal code must be exactly 6 digits'],
			index: true,
		},
		streetName: {
			type: String,
			required: [true, 'Please provide street name'],
			trim: true,
		},
		block: {
			type: String,
			trim: true,
		},
		houseNo: {
			type: String,
			required: [true, 'Please provide house number'],
			trim: true,
		},
		levelNo: {
			type: String,
			trim: true,
		},
		unitNo: {
			type: String,
			trim: true,
		},
		address: {
			type: String,
			required: true,
			trim: true,
		},
		profilePhoto: {
			type: String,
		},
		coverImage: {
			type: String,
		},
	},
	{ timestamps: true }
);

PersonalDetailsSchema.pre('save', function (next) {
	if (this.dob) {
		const today = new Date();
		const birthDate = new Date(this.dob);
		let age = today.getFullYear() - birthDate.getFullYear();
		const monthDiff = today.getMonth() - birthDate.getMonth();
		if (
			monthDiff < 0 ||
			(monthDiff === 0 && today.getDate() < birthDate.getDate())
		) {
			age--;
		}
		this.age = age;
	}
	next();
});

PersonalDetailsSchema.virtual('fullAddress').get(function () {
	return `${
		this.houseNo ? this.houseNo + ', ' : ''
	}${this.block ? 'Block ' + this.block + ', ' : ''}${this.levelNo ? 'Level ' + this.levelNo + ', ' : ''}${this.unitNo ? 'Unit ' + this.unitNo + ', ' : ''}${this.streetName}, Singapore ${this.postalCode}`;
});

PersonalDetailsSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
PersonalDetailsSchema.plugin(autoPopulate);
PersonalDetailsSchema.plugin(aggregatePaginate);

module.exports = mongoose.model('PersonalDetails', PersonalDetailsSchema);
