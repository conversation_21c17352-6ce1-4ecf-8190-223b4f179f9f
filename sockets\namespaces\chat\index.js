module.exports = (chatNamespace) => {
	chatNamespace.on('connection', (socket) => {
		console.log(
			`file: app.js:27 - io.on - new user connected socket:`,
			socket.id
		);

		socket.on('joinChat', (chatId) => {
			socket.join(chatId);
			// console.log(
			// 	`file: app.js:45 - socket.on -user joined chat chatId:`,
			// 	chatId
			// );
		});

		// Clean up on disconnect
		socket.on('disconnect', () => {
			console.log(`User disconnected: ${socket.id}`);
		});
	});
};
