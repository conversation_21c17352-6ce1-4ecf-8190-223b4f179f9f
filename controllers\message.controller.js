const { Message, Chat } = require('../models');
const { StatusCodes } = require('http-status-codes');
const { APIResponse } = require('../utils');
const { NotFoundError } = require('../errors');
const { setSocket } = require('../sockets');

const sendMessage = async (req, res) => {
	const io = setSocket();
	const chatNamespace = io.of('/chat');
	const { chatId, content, type } = req.body;

	const sender = req.user.userId;

	const message = await Message.create({
		chat: chatId,
		sender,
		content,
		type: type ?? 'text',
	});

	const chat = await Chat.findByIdAndUpdate(
		chatId,
		{
			lastMessage: message._id,
		},
		{ new: true }
	);

	if (chatNamespace) {
		chatNamespace.to(chatId).emit('newMessage', message);
	}

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'success', { message, chat }));
};

const getMessages = async (req, res) => {
	const { chatId } = req.params;
	const messages = await Message.find({ chat: chatId }).sort({ createdAt: 1 });
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'success', messages));
};

const markAsRead = async (req, res) => {
	const io = setSocket();
	const chatNamespace = io.of('/chat');

	const { messageId } = req.params;

	const message = await Message.findByIdAndUpdate(
		messageId,
		{
			isRead: true,
		},
		{ new: true }
	);

	if (!message) {
		throw new NotFoundError('Message not found');
	}

	if (chatNamespace) {
		chatNamespace.to(message.chat.toString()).emit('messageRead', {
			messageId,
			chatId: message.chat.toString(),
		});
	}

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'success', message));
};

module.exports = {
	sendMessage,
	getMessages,
	markAsRead,
	setSocket,
};
