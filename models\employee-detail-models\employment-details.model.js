const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

// const workingDaysEnum = {
// 	['5_DAYS']: '5_DAYS',
// 	['5.5_DAYS']: '5.5_DAYS',
// 	['6_DAYS']: '6_DAYS',
// 	['7_DAYS']: '7_DAYS',
// 	['ALTERNATE_SATURDAYS']: 'ALTERNATE_SATURDAYS',
// };

// const weekDaysEnum = {
// 	SUNDAY: 'SUNDAY',
// 	MONDAY: 'MONDAY',
// 	TUESDAY: 'TUESDAY',
// 	WEDNESDAY: 'WEDNESDAY',
// 	THURSDAY: 'THURSDAY',
// 	FRIDAY: 'FRIDAY',
// 	SATURDAY: 'SATURDAY',
// };

// const workingHoursEnum = {
// 	FOUR: 4,
// 	SIX: 6,
// 	EIGHT: 8,
// 	TEN: 10,
// 	TWELVE: 12,
// };

const EmploymentDetailsSchema = new mongoose.Schema(
	{
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
		source: {
			type: String,
			enum: ['staff-recommendation', 'job-advertisement'],
		},
		businessUnit: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'BusinessUnit',
			required: [true, 'Please provide business unit'],

			index: true,
		},
		department: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'Department',
			required: [true, 'Please provide department'],
			index: true,
		},
		designation: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'Designation',
			required: [true, 'Please provide designation'],
			index: true,
		},
		reportingTo: {
			type: mongoose.mongoose.Schema.Types.ObjectId,
			ref: 'User',
			required: [true, 'Please provide reporting manager'],
			index: true,
		},
		probationPeriod: {
			type: Number,
			min: [0, 'Probation period cannot be negative'],
			max: [12, 'Probation period cannot exceed 12 months'],
		},
		employmentType: {
			type: String,
			enum: ['part-time', 'full-time'],
			required: [true, 'Please specify employment type'],
		},
		workSchedule: {
			type: String,
			enum: ['shifts', 'generic'],
			required: [true, 'Please specify work schedule'],
		},

		// workingDays: {
		// 	type: String,
		// 	enum: Object.values(workingDaysEnum),
		// },
		// workingHours: {
		// 	type: Number,
		// 	enum: Object.values(workingHoursEnum),
		// },
		// halfDay: {
		// 	type: String,
		// 	enum: ['FIRST_HALF', 'SECOND_HALF'],
		// },
		// firstOffDay: {
		// 	type: String,
		// 	enum: Object.values(weekDaysEnum),
		// },
		// secondOffDay: {
		// 	type: String,
		// 	enum: Object.values(weekDaysEnum),
		// },

		overTimeEligible: {
			type: Boolean,
			default: false,
		},
		shiftId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'Shift',
			default: null,
		},
	},
	{ timestamps: true }
);
EmploymentDetailsSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
const EmploymentDetails = mongoose.model(
	'EmploymentDetails',
	EmploymentDetailsSchema
);
module.exports = {
	EmploymentDetails,
	// workingDaysEnum,
	// weekDaysEnum,
	// workingHoursEnum,
};
