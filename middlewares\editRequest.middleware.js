const { BadRequestError } = require('../errors');
const {
	getEditRequestsSchema,
	addEditRequestsSchema,
	updateEditRequestSchema,
	updateEditRequestStatusSchema,
	deleteEditRequestsSchema,
} = require('../schemas/edit-request.schema');

const getEditRequestsMiddleware = (req, res, next) => {
	const result = getEditRequestsSchema.safeParse(req.query);

	if (!result.success) {
		throw new BadRequestError(
			'Invalid query parameters',
			result.error.format()
		);
	}
	next();
};

const addEditRequestMiddleware = (req, res, next) => {
	const result = addEditRequestsSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError(
			'Invalid edit request data',
			result.error.format()
		);
	}
	next();
};

const updateEditRequestMiddleware = (req, res, next) => {
	const result = updateEditRequestSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError(
			'Invalid edit request update data',
			result.error.format()
		);
	}
	next();
};

const updateEditRequestStatusMiddleware = (req, res, next) => {
	const result = updateEditRequestStatusSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError(
			'Invalid edit request status update data',
			result.error.format()
		);
	}
	next();
};

const deleteEditRequestsMiddleware = (req, res, next) => {
	const result = deleteEditRequestsSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError(
			'Invalid edit request deletion data',
			result.error.format()
		);
	}
	next();
};

module.exports = {
	getEditRequestsMiddleware,
	addEditRequestMiddleware,
	updateEditRequestMiddleware,
	updateEditRequestStatusMiddleware,
	deleteEditRequestsMiddleware,
};
