const { StatusCodes } = require('http-status-codes');
const {
	BadRequestError,
	UnauthorizedError,
	ConflictError,
} = require('../errors');
const crypto = require('crypto');
const {
	passwordPattern,
	attachCookiesToResponse,
	sendVerificationEmail,
	APIResponse,
	sendResetPasswordEmail,
	logger,
	isMobileRequest,
	generateToken,
	createHash,
} = require('../utils');
const {
	User,
	Token,
	Country,
	PersonalDetails,
	QRSession,
} = require('../models');
const { userRoles } = require('../models/user.model');
const dayjs = require('dayjs');
const { setSocket } = require('../sockets');
const { sessionMap } = require('../sockets/namespaces/auth/sessionMap');

const getAuthenticatedUser = (req, res) => {
	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'success', {
			authenticatedUser: req.user,
		})
	);
};

const verifyEmail = async (req, res) => {
	const { verificationToken, email } = req.body;
	const user = await User.findOne({ email });
	if (!user) {
		throw new UnauthorizedError('Invalid Session');
	}

	const isVerificationTokenExpired = dayjs().isAfter(
		user.verificationTokenExpiry
	);

	if (verificationToken !== user.verificationToken) {
		throw new UnauthorizedError('Verification Failed, Invalid OTP');
	}

	if (isVerificationTokenExpired) {
		user.verificationToken = '';
		user.verificationTokenExpiry = null;
		await user.save();
		throw new UnauthorizedError('Verification Failed, Token Expired');
	}

	user.verificationToken = '';
	user.isVerified = true;
	user.verificationTokenExpiry = null;
	user.verified = new Date(Date.now());
	await user.save();

	let refreshToken = '';
	const existingToken = await Token.findOne({ user: user._id });
	if (existingToken) {
		const { isValid } = existingToken;
		if (!isValid) {
			throw new UnauthorizedError('Authentication Failed, Contact Admin');
		}
		refreshToken = existingToken.refreshToken;
		if (isMobileRequest(req)) {
			const { accessTokenJWT, refreshTokenJWT } = generateToken({
				user,
				refreshToken,
			});
			return res.status(StatusCodes.OK).json(
				new APIResponse(StatusCodes.OK, 'Login Successful', {
					accessToken: accessTokenJWT,
					refreshToken: refreshTokenJWT,
				})
			);
		}
		attachCookiesToResponse({ res, user, refreshToken });
		return res
			.status(StatusCodes.OK)
			.json(new APIResponse(StatusCodes.OK, 'Login Successful'));
	}

	refreshToken = crypto.randomBytes(40).toString('hex');
	const userAgent = req.headers['user-agent'];
	const ip = req.clientIp;
	await Token.create({ refreshToken, ip, userAgent, user: user._id });
	if (isMobileRequest(req)) {
		const { accessTokenJWT, refreshTokenJWT } = generateToken({
			user,
			refreshToken,
		});
		return res.status(StatusCodes.OK).json(
			new APIResponse(StatusCodes.OK, 'Email Verified Successfully', {
				accessToken: accessTokenJWT,
				refreshToken: refreshTokenJWT,
			})
		);
	}
	attachCookiesToResponse({ res, user, refreshToken });

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Email Verified Successfully'));
};

const login = async (req, res) => {
	const { email, password, rememberMe } = req.body;
	if (!email || !password) {
		throw new BadRequestError('Please enter email and password');
	}
	const user = await User.findOne({ email });
	if (!user) {
		throw new UnauthorizedError('Invalid Email');
	}
	const isPasswordCorrect = await user.comparePassword(password);
	if (!isPasswordCorrect) {
		throw new UnauthorizedError('Invalid Credentials');
	}
	if (!user.isVerified) {
		throw new UnauthorizedError('Please verify your email');
	}

	// console.log(`ip ${req.ip}`);
	// console.log(`ip ${req.connection.remoteAddress}`);
	// console.log(req.headers["x-forwarded-for"]?.split(",").shift());
	// console.log(req.socket.remoteAddress);

	let refreshToken = '';
	const existingToken = await Token.findOne({ user: user._id });
	if (existingToken) {
		const { isValid } = existingToken;
		if (!isValid) {
			throw new UnauthorizedError('Authentication Failed, Contact Admin');
		}
		refreshToken = existingToken.refreshToken;
		if (isMobileRequest(req)) {
			const { accessTokenJWT, refreshTokenJWT } = generateToken({
				user,
				refreshToken,
			});
			return res.status(StatusCodes.OK).json(
				new APIResponse(StatusCodes.OK, 'Login Successful', {
					accessToken: accessTokenJWT,
					refreshToken: refreshTokenJWT,
				})
			);
		}
		attachCookiesToResponse({ res, user, refreshToken });
		res
			.status(StatusCodes.OK)
			.json(new APIResponse(StatusCodes.OK, 'Login Successful'));
		return;
	}

	refreshToken = crypto.randomBytes(40).toString('hex');
	const userAgent = req.headers['user-agent'];
	const ip = req.ip;
	await Token.create({ refreshToken, ip, userAgent, user: user._id });
	if (isMobileRequest(req)) {
		const { accessTokenJWT, refreshTokenJWT } = generateToken({
			user,
			refreshToken,
		});
		return res.status(StatusCodes.OK).json(
			new APIResponse(StatusCodes.OK, 'Login Successful', {
				accessToken: accessTokenJWT,
				refreshToken: refreshTokenJWT,
			})
		);
	}
	attachCookiesToResponse({ res, user, refreshToken, rememberMe });
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Login Successful'));
};

const logout = async (req, res) => {
	await Token.findOneAndDelete({ user: req.user.userId });
	res.clearCookie('accessToken');
	res.clearCookie('refreshToken');
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Logout Successful'));
};

const registerEmail = async (req, res) => {
	const { email } = req.body;

	let user = await User.findOne({ email });
	if (user && !user.password && !user.isVerified) {
		const verificationToken = crypto.randomInt(100000, 1000000).toString();
		const verificationTokenExpiry = dayjs().add(3, 'minute');
		user.verificationToken = verificationToken;
		user.verificationTokenExpiry = verificationTokenExpiry;
		await user.save();
		sendVerificationEmail({
			email: user.email,
			verificationToken: user.verificationToken,
		});
		res.status(StatusCodes.OK).json(
			new APIResponse(StatusCodes.OK, 'Please check your email', {
				isPending: true,
			})
		);
		return;
	}
	if (user && user.isVerified && !user.password) {
		res.status(StatusCodes.OK).json(
			new APIResponse(StatusCodes.OK, 'Please complete your registration', {
				isPendingRegistration: true,
			})
		);
		return;
	}
	if (user && user.password) {
		throw new ConflictError(
			'Looks like you are already registered, please login'
		);
	}
	const isFirst = (await User.countDocuments({})) === 0;
	const role = isFirst ? userRoles.SUPER_ADMIN : userRoles.CLIENT_ADMIN;
	const verificationToken = crypto.randomInt(100000, 1000000).toString();
	const verificationTokenExpiry = dayjs().add(3, 'minute');

	user = await User.create({
		email: email.toLowerCase(),
		role,
		verificationToken,
		verificationTokenExpiry,
	});
	sendVerificationEmail({
		email: user.email,
		verificationToken: user.verificationToken,
	});
	res
		.status(StatusCodes.CREATED)
		.json(
			new APIResponse(
				StatusCodes.CREATED,
				'Success! Please check your email to verify account'
			)
		);
};

const register = async (req, res) => {
	const { email, password } = req.body;
	if (!email || !password) {
		throw new BadRequestError('Please provide all values');
	}

	if (!passwordPattern.test(password)) {
		throw new BadRequestError(
			'Password must be at least 8 characters long, include at least one uppercase letter, one lowercase letter, one number, and one special character.'
		);
	}
	const user = await User.findOne({ email });
	user.password = password;
	await user.save();
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Registration Successful'));
};

const forgotPassword = async (req, res) => {
	const { email } = req.body;

	const user = await User.findOne({ email });

	// We wont throw any error if user not found instead we will simply return success response
	if (!user) {
		logger.info(
			`Forgot password request from ${req.ip} with user-agent ${req.headers['user-agent']} recorded as user was not found in our DB, probably someone is trying to access our api`
		);
		res
			.status(StatusCodes.OK)
			.json(new APIResponse(StatusCodes.OK, 'Reset link sent to your email'));
		return;
	}

	const resetPasswordToken = crypto.randomBytes(70).toString('hex');
	const resetPasswordTokenExpiry = dayjs().add(3, 'minute');
	user.resetPasswordToken = createHash(resetPasswordToken);
	user.resetPasswordTokenExpiry = resetPasswordTokenExpiry;
	await user.save();

	sendResetPasswordEmail({
		email: user.email,
		resetToken: resetPasswordToken,
		origin:
			process.env.NODE_ENV === 'production'
				? process.env.FRONTEND_ORIGIN
				: process.env.DEVELOPMENT_ORIGIN,
	});

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Reset link sent to your email'));
};

const resetPassword = async (req, res) => {
	const { token, email, password } = req.body;

	const user = await User.findOne({ email });
	const isResetTokenExpired = dayjs().isAfter(user.resetPasswordTokenExpiry);
	if (isResetTokenExpired) {
		throw new BadRequestError('Reset token has expired');
	}

	if (createHash(token) !== user.resetPasswordToken) {
		throw new BadRequestError('Invalid reset token');
	}

	user.password = password;
	user.resetPasswordToken = null;
	user.resetPasswordTokenExpiry = null;
	await user.save();

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Password reset successfully'));
};

const loginForMobile = async (req, res) => {
	const { countryId, mobile, password } = req.body;

	const country = await Country.findOne({ _id: countryId });

	if (!country) {
		throw new NotFoundError('Country not found');
	}

	const userDetails = await PersonalDetails.findOne({ mobile, deleted: false });
	if (!userDetails) {
		throw new UnauthorizedError('Invalid Credentials', [
			'user with the provided mobile number not found',
		]);
	}
	const user = await User.findOne({ _id: userDetails.userId });

	if (!user) {
		throw new UnauthorizedError('Invalid credentials');
	}

	const isPasswordCorrect = await user.comparePassword(password);

	if (!isPasswordCorrect) {
		throw new UnauthorizedError('Invalid Password');
	}

	let refreshToken = '';
	const existingToken = await Token.findOne({ user: user._id });
	if (existingToken) {
		const { isValid } = existingToken;
		if (!isValid) {
			throw new UnauthorizedError('Authentication Failed, Contact Admin');
		}
		refreshToken = existingToken.refreshToken;
		if (isMobileRequest(req)) {
			const { accessTokenJWT, refreshTokenJWT } = generateToken({
				user,
				refreshToken,
			});
			return res.status(StatusCodes.OK).json(
				new APIResponse(StatusCodes.OK, 'Login Successful', {
					accessToken: accessTokenJWT,
					refreshToken: refreshTokenJWT,
				})
			);
		}
		attachCookiesToResponse({ res, user, refreshToken });
		res
			.status(StatusCodes.OK)
			.json(new APIResponse(StatusCodes.OK, 'Login Successful'));
		return;
	}

	refreshToken = crypto.randomBytes(40).toString('hex');
	const userAgent = req.headers['user-agent'];
	const ip = req.ip;
	await Token.create({ refreshToken, ip, userAgent, user: user._id });
	if (isMobileRequest(req)) {
		const { accessTokenJWT, refreshTokenJWT } = generateToken({
			user,
			refreshToken,
		});
		return res.status(StatusCodes.OK).json(
			new APIResponse(StatusCodes.OK, 'Login Successful', {
				accessToken: accessTokenJWT,
				refreshToken: refreshTokenJWT,
			})
		);
	}
	attachCookiesToResponse({ res, user, refreshToken });
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Login Successful'));
};

const loginUsingQr = async (req, res) => {
	const { qrSession: qrSessionId, user } = req;

	const io = setSocket();
	const authNamespace = io.of('/auth');

	await QRSession.create({
		qrSessionId,
		user: req.user.userId,
		status: 'confirmed',
		confirmedAt: dayjs().toISOString(),
	});
	authNamespace
		.to(qrSessionId)
		.emit('login-successful', { email: req.user.email, token: qrSessionId });

	sessionMap.delete(qrSessionId);

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Scan Successful, Login initiated'));
};

const confirmQrSessionLogin = async (req, res) => {
	const { email, token } = req.body;

	const user = await User.findOne({ email });

	if (!user) {
		throw new NotFoundError('User not found');
	}

	const existingQrSession = await QRSession.findOne({ user: user._id });
	if (
		existingQrSession.status !== 'confirmed' &&
		existingQrSession.qrSessionId !== token
	) {
		throw new UnauthorizedError('Invalid session');
	}

	let refreshToken = '';
	const existingToken = await Token.findOne({ user: user._id });
	if (existingToken) {
		const { isValid } = existingToken;
		if (!isValid) {
			throw new UnauthorizedError('Authentication Failed, Contact Admin');
		}
		refreshToken = existingToken.refreshToken;
		attachCookiesToResponse({ res, user, refreshToken });
		res.status(StatusCodes.OK).json(
			new APIResponse(StatusCodes.OK, 'Login Successful', {
				authenticatedUser: user,
			})
		);
		return;
	}

	refreshToken = crypto.randomBytes(40).toString('hex');
	const userAgent = req.headers['user-agent'];
	const ip = req.ip;
	await Token.create({ refreshToken, ip, userAgent, user: user._id });
	attachCookiesToResponse({ res, user, refreshToken });
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Login Successful'));
};

module.exports = {
	login,
	logout,
	register,
	forgotPassword,
	resetPassword,
	verifyEmail,
	loginForMobile,
	registerEmail,
	getAuthenticatedUser,
	loginUsingQr,
	confirmQrSessionLogin,
};
