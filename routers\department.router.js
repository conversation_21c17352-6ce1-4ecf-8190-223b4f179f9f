const express = require('express');
const router = express.Router();

// Controllers
const {
	getAllDepartments,
	addDepartment,
	updateDepartment,
	deleteDepartment,
	getDepartmentDesignations,
} = require('../controllers/department.controller');

// Middlewares
const {
	authenticationMiddleware,
	addDepartmentMiddleware,
	updateDepartmentMiddleware,
} = require('../middlewares');

const {
	deleteDepartmentMiddleware,
} = require('../middlewares/company-details.middleware');

/**
 * @route   GET /
 * @desc    Get all departments
 * @access  Private (Authenticated)
 */
router.route('/').get(authenticationMiddleware, getAllDepartments);

/**
 * @route   POST /
 * @desc    Add a new department
 * @access  Private (Authenticated)
 */
router
	.route('/')
	.post(authenticationMiddleware, addDepartmentMiddleware, addDepartment);

/**
 * @route   PATCH /
 * @desc    Update a department
 * @access  Private (Authenticated)
 */
router
	.route('/')
	.patch(
		authenticationMiddleware,
		updateDepartmentMiddleware,
		updateDepartment
	);

/**
 * @route   PATCH /remove
 * @desc    Soft delete a department
 * @access  Private (Authenticated)
 */
router
	.route('/remove')
	.patch(
		authenticationMiddleware,
		deleteDepartmentMiddleware,
		deleteDepartment
	);

/**
 * @route   GET /:id/designations
 * @desc    Get designations under a specific department
 * @access  Private (Authenticated)
 */
router
	.route('/:id/designations')
	.get(authenticationMiddleware, getDepartmentDesignations);

module.exports = router;
