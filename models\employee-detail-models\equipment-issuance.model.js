const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

const EquipmentIssuanceSchema = new mongoose.Schema(
	{
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
		equipmentName: {
			type: String,
			required: [true, 'Equipment name is required'],
		},
		brand: {
			type: String,
			required: [true, 'Brand is required'],
		},
		model: {
			type: String,
			required: [true, 'Model is required'],
		},
		serialNumber: {
			type: String,
			required: [true, 'Serial number is required'],
			unique: true,
			index: true,
		},
		issueDate: {
			type: Date,
			required: [true, 'Issue date is required'],
			default: Date.now,
		},
		returnDate: {
			type: Date,
		},
		issueReason: {
			type: String,
			enum: ['new-hire', 'replacement', 'repair', 'other'],
			required: [true, 'Issue reason is required'],
		},
		returnReason: {
			type: String,
			enum: ['damaged', 'end-of-contract', 'upgrade', 'other'],
		},
		otherIssueReason: {
			type: String,
		},
		otherReturnReason: {
			type: String,
		},
		assetTag: {
			type: String,
			unique: true,
			required: [true, 'Asset tag is required'],
			index: true,
		},
	},
	{ timestamps: true }
);

EquipmentIssuanceSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
EquipmentIssuanceSchema.plugin(aggregatePaginate);
EquipmentIssuanceSchema.plugin(autoPopulate);

module.exports = mongoose.model('EquipmentIssuance', EquipmentIssuanceSchema);
