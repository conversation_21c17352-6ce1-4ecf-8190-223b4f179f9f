// Helper function to deeply compare two objects
const deepEqual = (obj1, obj2) => {
	if (obj1 === obj2) return true;

	if (obj1 == null || obj2 == null) return obj1 === obj2;

	if (typeof obj1 !== 'object' || typeof obj2 !== 'object')
		return obj1 === obj2;

	if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;

	if (Array.isArray(obj1)) {
		if (obj1.length !== obj2.length) return false;
		for (let i = 0; i < obj1.length; i++) {
			if (!deepEqual(obj1[i], obj2[i])) return false;
		}
		return true;
	}

	const keys1 = Object.keys(obj1);
	const keys2 = Object.keys(obj2);

	if (keys1.length !== keys2.length) return false;

	for (let key of keys1) {
		if (!keys2.includes(key) || !deepEqual(obj1[key], obj2[key])) return false;
	}

	return true;
};

// Helper function to filter oldData to only include fields present in newData
const filterOldData = (oldData, newData) => {
	if (!oldData || !newData) return oldData;

	if (typeof newData !== 'object' || Array.isArray(newData)) return oldData;

	const filtered = {};

	for (const key in newData) {
		if (newData.hasOwnProperty(key)) {
			if (oldData.hasOwnProperty(key)) {
				if (
					typeof newData[key] === 'object' &&
					!Array.isArray(newData[key]) &&
					newData[key] !== null
				) {
					// Recursively filter nested objects
					filtered[key] = filterOldData(oldData[key], newData[key]);
				} else {
					// Include the field from oldData
					filtered[key] = oldData[key];
				}
			}
			// If oldData doesn't have this field, that's fine - we don't add it
		}
	}

	return filtered;
};

module.exports = { deepEqual, filterOldData };
