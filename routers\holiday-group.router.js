const express = require('express');
const router = express.Router();

// Controllers
const {
	getHolidayGroups,
	addHolidayGroup,
	updateHolidayGroup,
	copyHolidayGroup,
	deleteHolidayGroup,
	getHolidayGroupDetails,
} = require('../controllers/holiday-group.controller');
const {
	getEmployeesByHolidayGroupId,
	removeEmployeeFromHolidayGroup,
} = require('../controllers/employee.controller');

// Middlewares
const {
	addHolidayGroupMiddleware,
	updateHolidayGroupMiddleware,
	copyHolidayGroupMiddleware,
	deleteHolidayGroupMiddleware,
} = require('../middlewares/company-details.middleware');
const {
	authenticationMiddleware,
	authorizePermissions,
} = require('../middlewares');

const { userRoles } = require('../models/user.model');

/**
 * @route   GET /
 * @desc    Get all holiday groups
 * @access  Private (Authenticated)
 */
router.route('/').get(authenticationMiddleware, getHolidayGroups);

/**
 * @route   GET /:id
 * @desc    Get holiday group details by ID
 * @access  Private (Authenticated)
 */
router.route('/:id').get(authenticationMiddleware, getHolidayGroupDetails);

/**
 * @route   POST /
 * @desc    Add a new holiday group
 * @access  Private (Authenticated)
 */
router
	.route('/')
	.post(authenticationMiddleware, addHolidayGroupMiddleware, addHolidayGroup);

/**
 * @route   PATCH /
 * @desc    Update a holiday group
 * @access  Private (Authenticated)
 */
router
	.route('/')
	.patch(
		authenticationMiddleware,
		updateHolidayGroupMiddleware,
		updateHolidayGroup
	);

/**
 * @route   POST /copy
 * @desc    Copy a holiday group
 * @access  Private (Authenticated)
 */
router
	.route('/copy')
	.post(authenticationMiddleware, copyHolidayGroupMiddleware, copyHolidayGroup);

/**
 * @route   PATCH /remove
 * @desc    Soft delete a holiday group
 * @access  Private (Authenticated)
 */
router
	.route('/remove')
	.patch(
		authenticationMiddleware,
		deleteHolidayGroupMiddleware,
		deleteHolidayGroup
	);

/**
 * @route   GET /:holidayGroupId/employees
 * @desc    Get employees by holiday group ID
 * @access  Private (GLORIFIED_CLIENT_ADMIN, CLIENT_ADMIN)
 */
router.get(
	'/:holidayGroupId/employees',
	authenticationMiddleware,
	authorizePermissions(
		userRoles.GLORIFIED_CLIENT_ADMIN,
		userRoles.CLIENT_ADMIN
	),
	getEmployeesByHolidayGroupId
);

/**
 * @route   PATCH /:holidayGroupId/employees/:employeeId
 * @desc    Remove employee from holiday group
 * @access  Private (GLORIFIED_CLIENT_ADMIN, CLIENT_ADMIN)
 */
router.patch(
	'/:holidayGroupId/employees/:employeeId',
	authenticationMiddleware,
	authorizePermissions(
		userRoles.GLORIFIED_CLIENT_ADMIN,
		userRoles.CLIENT_ADMIN
	),
	removeEmployeeFromHolidayGroup
);

module.exports = router;
