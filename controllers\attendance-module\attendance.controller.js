const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
const {
	NotFoundError,
	BadRequestError,
	UnauthorizedError,
} = require('../../errors');
const {
	EmploymentDetails,
	Shift,
	TimeLog,
	CompanyDetails,
	User,
} = require('../../models');
const { StatusCodes } = require('http-status-codes');
const {
	APIResponse,
	calculateDuration,
	getCurrentTime,
	getCustomTime,
	getTimeLogForToday,
	checkPermissions,
} = require('../../utils');
const {
	generateDefaultShiftSetting,
} = require('../../models/attendance-module-models/shifts-setting.model');
const {
	getEmployeeTimeSheetAggregation,
	getTimeSheetForEmployee,
} = require('../../db/aggregations/attendance.aggregation');
const { userRoles } = require('../../models/user.model');

dayjs.extend(utc);
dayjs.extend(timezone);

const employeeClockIn = async (req, res) => {
	const { userId, companyId } = req.user;

	// Get employee details
	const employee = await EmploymentDetails.findOne({ userId });
	const company = await CompanyDetails.findOne({ _id: companyId });
	if (!employee) {
		throw new NotFoundError('Employee not found');
	}

	// Use employee's timezone, fallback to UTC
	const tz = company?.businessCountry?.timezones[0]?.zoneName || 'UTC';

	// Get shift details
	let shift = await Shift.findOne({ companyId, _id: employee.shiftId });
	if (!shift) {
		throw new NotFoundError('Shift not found');
	}

	// FIXME: this creating shift is temporary until the settings is not assigned to employees for testing purpose, we will remove the default shift part and uncomment the above error part
	// If shift is not found, find the default shift
	// if (!shift) {
	// 	shift = await Shift.findOne({ companyId, isDefault: true });
	// 	// If default shift is also not found, create one
	// 	if (!shift) {
	// 		shift = await generateDefaultShiftSetting({ companyId });
	// 	}
	// 	employee.shiftId = shift._id;
	// 	await employee.save();
	// }

	const {
		startTime,
		endTime,
		clockOutDelay,
		allowedAttendanceDays,
		maxClockInTime,
	} = shift;

	const currentTime = getCurrentTime(tz);
	const currentDay = currentTime.format('dddd').toLowerCase();

	// Check if today is an allowed attendance day
	if (!allowedAttendanceDays.includes(currentDay)) {
		throw new BadRequestError('Attendance not allowed on this day');
	}

	// Check if employee has clocked out already
	const todayStart = dayjs().tz(tz).startOf('day').toDate();
	const todayEnd = dayjs().tz(tz).endOf('day').toDate();
	const existingTimeLog = await TimeLog.findOne({
		userId,
		createdAt: { $gte: todayStart, $lte: todayEnd },
	}).sort({ createdAt: -1 });

	const existingTimeLogClockOut = existingTimeLog?.attendanceTime.find(
		(log) => log.type === 'clockOut'
	)?.time;
	const formattedClockOutTime = getCustomTime({
		customDate: existingTimeLogClockOut,
		tz,
	});

	if (
		existingTimeLogClockOut &&
		currentTime.diff(formattedClockOutTime, 'minute') < clockOutDelay
	) {
		const nextAllowed = dayjs(existingTimeLogClockOut)
			.tz(tz)
			.add(clockOutDelay, 'minute')
			.format('h:mm A');

		throw new BadRequestError(`You cannot clock in again until ${nextAllowed}`);
	}

	// Prevent duplicate clock-ins on the same day
	const alreadyClockedIn = existingTimeLog?.attendanceTime.some(
		(log) =>
			log.type === 'clockIn' &&
			dayjs(log.time).tz(tz).isSame(currentTime, 'day')
	);

	if (alreadyClockedIn) {
		throw new BadRequestError('You have already clocked in today.');
	}

	// Parse shift start and end time in employee's timezone
	const [startHour, startMinute] = startTime.split(':').map(Number);
	const [endHour, endMinute] = endTime.split(':').map(Number);

	const formattedStartTime = dayjs()
		.tz(tz)
		.hour(startHour)
		.minute(startMinute)
		.second(0);
	const formattedEndTime = dayjs()
		.tz(tz)
		.hour(endHour)
		.minute(endMinute)
		.second(0);
	const tardiness = currentTime.diff(formattedStartTime, 'minute');

	if (currentTime.isBefore(formattedStartTime)) {
		throw new BadRequestError(
			`You cannot clock in before ${formattedStartTime.format('h:mm A')}`
		);
	}

	if (
		currentTime.isSame(formattedEndTime) ||
		currentTime.isAfter(formattedEndTime)
	) {
		throw new BadRequestError(
			'Your shift has ended. If you want to work overtime, please generate a request for overtime.'
		);
	}

	// Optional: Holiday check (commented)
	// const isHoliday = await Holiday.findOne({ date: currentTime.format('YYYY-MM-DD'), companyId });
	// if (isHoliday) {
	//   throw new BadRequestError('Today is a holiday. Attendance is not allowed.');
	// }

	// Clock-in limit check
	const maxClockInTimeThreshold = formattedStartTime.add(
		maxClockInTime,
		'minute'
	);
	if (currentTime.isAfter(maxClockInTimeThreshold)) {
		throw new BadRequestError(
			`Clock-in time exceeded. Allowed till ${maxClockInTimeThreshold.format('h:mm A')}`
		);
	}

	const timeLog = await TimeLog.create({
		userId,
		attendanceTime: [
			{
				type: 'clockIn',
				time: currentTime.toISOString(), // Save as Date object in UTC
			},
		],
		expectedWorkingHours: employee.workingHours,
		isOvertimeEligible: employee.isOvertimeEligible,
		startTime,
		endTime,
		clockInLimit: shift.clockInLimit,
		clockOutLimit: shift.clockOutLimit,
		clockInDelay: shift.clockInDelay,
		clockOutDelay: shift.clockOutDelay,
		breakLimit: shift.breakLimit,
		approvalStatus: shift.isApprovalStatusEnabled ? 'pending' : 'not-required',
		lateInMinutes: tardiness > 0 ? tardiness : 0,
	});

	res
		.status(StatusCodes.CREATED)
		.json(
			new APIResponse(StatusCodes.CREATED, 'Clocked in successfully', timeLog)
		);
};

const startBreak = async (req, res) => {
	const { userId, companyId } = req.user;
	const { isOvertime = false } = req.body;

	const company = await CompanyDetails.findOne({ _id: companyId });
	const tz = company?.businessCountry?.timezones[0]?.zoneName || 'UTC';

	const currentTime = getCurrentTime(tz);
	const timeLog = await getTimeLogForToday(userId);

	if (!timeLog) {
		throw new BadRequestError('No clock-in record found for today.');
	}

	const breakCount = timeLog.attendanceTime.filter(
		(entry) => entry.type === 'start-break'
	).length;

	if (breakCount >= timeLog.breakLimit) {
		throw new BadRequestError('Maximum break limit reached.');
	}

	const isBreakInProgress =
		timeLog.attendanceTime[timeLog.attendanceTime.length - 1].type ===
		'start-break';

	if (isBreakInProgress) {
		throw new BadRequestError('Already on break');
	}

	const lastEndBreak = [...timeLog.attendanceTime]
		.reverse()
		.find((entry) => entry.type === 'end-break');

	// if (!lastStartBreak) {
	// 	throw new BadRequestError('No break start record found.');
	// }
	if (lastEndBreak) {
		const postBreakDuration = calculateDuration(lastEndBreak.time, currentTime);
		timeLog.actualWorkingHours =
			(timeLog.actualWorkingHours || 0) + postBreakDuration;
	} else {
		const clockInTime = timeLog.attendanceTime.find(
			(log) => log.type === 'clockIn'
		)?.time;
		const totalWorked = calculateDuration(clockInTime, currentTime);
		timeLog.actualWorkingHours =
			(timeLog.actualWorkingHours || 0) + totalWorked;
	}

	timeLog.attendanceTime.push({
		type: 'start-break',
		time: currentTime.toISOString(),
	});
	await timeLog.save();

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Break started successfully.'));
};

const endBreak = async (req, res) => {
	const { userId, companyId } = req.user;
	const { isOvertime = false } = req.body;
	const company = await CompanyDetails.findOne({ _id: companyId });
	const tz = company?.businessCountry?.timezones[0]?.zoneName || 'UTC';

	const currentTime = getCurrentTime(tz);
	const timeLog = await getTimeLogForToday(userId);

	if (!timeLog) {
		throw new BadRequestError('No clock-in record found for today.');
	}

	timeLog.attendanceTime.push({
		type: 'end-break',
		time: currentTime.toISOString(),
	});
	await timeLog.save();

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Break ended successfully.'));
};

const clockOut = async (req, res) => {
	const { userId, companyId } = req.user;
	const { isOvertime = false } = req.body;

	const company = await CompanyDetails.findOne({ _id: companyId });
	const tz = company?.businessCountry?.timezones[0]?.zoneName || 'UTC';

	const currentTime = getCurrentTime(tz);
	const timeLog = await getTimeLogForToday(userId);

	if (!timeLog) {
		throw new BadRequestError('No clock-in record found for today.');
	}

	const clockInEntry = timeLog.attendanceTime.find(
		(entry) => entry.type === 'clockIn'
	);

	if (!clockInEntry) {
		throw new BadRequestError('No clock-in time found.');
	}

	const clockOutEntry = [...timeLog.attendanceTime]
		.reverse()
		.find((entry) => entry.type === 'clockOut');

	if (clockOutEntry) {
		throw new BadRequestError('You have already clocked out.');
	}

	const minWorkDuration = timeLog.clockInLimit || 0;
	const workedDuration = calculateDuration(clockInEntry.time, currentTime);

	// if (workedDuration < minWorkDuration) {
	// 	throw new BadRequestError(
	// 		`You must work at least ${minWorkDuration} minutes before clocking out.`
	// 	);
	// }

	const lastEndBreak = [...timeLog.attendanceTime]
		.reverse()
		.find((entry) => entry.type === 'end-break');

	if (lastEndBreak) {
		const postBreakDuration = calculateDuration(lastEndBreak.time, currentTime);
		timeLog.actualWorkingHours =
			(timeLog.actualWorkingHours || 0) + postBreakDuration;
	}
	const [endHour, endMinute] = timeLog.endTime.split(':').map(Number);
	const formattedEndTime = dayjs()
		.tz(tz)
		.hour(endHour)
		.minute(endMinute)
		.second(0);
	const shiftEndTime = getCustomTime({ tz, customDate: formattedEndTime });
	if (currentTime.isBefore(shiftEndTime)) {
		timeLog.earlyOutMinutes = calculateDuration(currentTime, shiftEndTime);
	}

	timeLog.attendanceTime.push({
		type: 'clockOut',
		time: currentTime.toISOString(),
	});
	timeLog.clockOutBy = 'user';
	await timeLog.save();

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Clocked out successfully.'));
};

const getCurrentAttendanceStatus = async (req, res) => {
	const { userId, companyId } = req.user;
	const { isOvertime = false } = req.body;

	const company = await CompanyDetails.findOne({ _id: companyId });
	const tz = company?.businessCountry?.timezones[0]?.zoneName || 'UTC';
	const currentTime = getCurrentTime(tz);

	const timeLog = await getTimeLogForToday(userId);

	if (!timeLog) {
		return res.status(StatusCodes.OK).json(
			new APIResponse(StatusCodes.OK, 'User has not clocked in', {
				status: 'notClockIn',
			})
		);
	}

	const clockInEntry = timeLog.attendanceTime.find(
		(entry) => entry.type === 'clockIn'
	);

	if (!clockInEntry) {
		return res.status(StatusCodes.OK).json(
			new APIResponse(StatusCodes.OK, 'User has not clocked in', {
				status: 'notClockIn',
			})
		);
	}

	const startBreaks = timeLog.attendanceTime.filter(
		(e) => e.type === 'start-break'
	);
	const endBreaks = timeLog.attendanceTime.filter(
		(e) => e.type === 'end-break'
	);

	if (startBreaks.length > endBreaks.length) {
		return res.status(StatusCodes.OK).json(
			new APIResponse(StatusCodes.OK, 'User is currently on break', {
				status: 'onBreak',
			})
		);
	}

	const hasClockOut = timeLog.attendanceTime.some(
		(entry) => entry.type === 'clockOut'
	);

	if (hasClockOut) {
		return res.status(StatusCodes.OK).json(
			new APIResponse(StatusCodes.OK, 'User has clocked out', {
				status: 'clockOut',
			})
		);
	}

	return res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'User is currently in office', {
			status: 'inOffice',
		})
	);
};

const getTimeLogs = async (req, res) => {
	const { userId } = req.user;
	const timeLog = await getTimeLogForToday(userId);
	if (!timeLog) {
		throw new BadRequestError('No time log found for today');
	}
	return res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, 'Time logs fetched successfully', timeLog)
		);
};

const getTimeSheet = async (req, res) => {
	const { companyId } = req.user;
	const { userId, month } = req.query;
	let { year } = req.query;

	year = year ?? parseInt(dayjs().format('YYYY'));
	const startDate = dayjs(
		`${year}-${month.toString().padStart(2, '0')}-01`
	).startOf('day');
	const endDate = startDate.endOf('month').add(1, 'day').startOf('day');

	const allDays = [];
	let current = startDate;
	while (current.isBefore(endDate)) {
		allDays.push(current.format('YYYY-MM-DD'));
		current = current.add(1, 'day');
	}

	let timeSheet;
	if (userId) {
		checkPermissions(req.user, userId);
		timeSheet = await User.aggregate(
			getTimeSheetForEmployee({
				startDate: startDate.toISOString(),
				endDate: endDate.toISOString(),
				allDays,
				userId,
			})
		);
	} else {
		if (req.user.role > 6) {
			throw new UnauthorizedError('You are not authorized to view this page');
		}
		const page = parseInt(req.query.page) || 1;
		let limit = parseInt(req.query.page) || 10;
		if (req.query.limit === 'all') {
			limit = await User.countDocuments({ companyId: companyId._id });
		}
		const options = { limit, page };
		const timeSheetAggregateQuery = User.aggregate(
			getEmployeeTimeSheetAggregation({
				allDays,
				startDate: startDate.toDate(),
				endDate: endDate.toDate(),
				companyId: companyId._id,
			})
		);
		timeSheet = await User.aggregatePaginate(timeSheetAggregateQuery, options);
	}

	const respData = userId
		? timeSheet[0]
		: {
				timeSheet: timeSheet.docs,
				totalPages: timeSheet.totalPages,
				currentPage: timeSheet.page,
				total: timeSheet.totalDocs,
			};

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, 'Logs Fetched Successfully', respData)
		);
};

module.exports = {
	employeeClockIn,
	startBreak,
	endBreak,
	clockOut,
	getCurrentAttendanceStatus,
	getTimeLogs,
	getTimeSheet,
};
