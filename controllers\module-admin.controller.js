const { StatusCodes } = require('http-status-codes');
const { NotFoundError } = require('../errors');
const { User } = require('../models');
const { userRoles, modules } = require('../models/user.model');
const { APIResponse } = require('../utils');
const {
	getModuleAdminsAggregation,
} = require('../db/aggregations/employee.aggregation');

const createModuleAdmin = async (req, res) => {
	const { employeeId } = req.body;

	const employee = await User.findOne({
		_id: employeeId,
		deleted: false,
		companyId: req.user.companyId,
	});
	if (!employee) {
		throw new NotFoundError('Employee not found');
	}

	if (req.user.clientAdminId !== employee.clientAdminId) {
		throw new NotFoundError(
			'Employee not found or does not belong to the same company',
			[
				'Employee not found or does not belong to the same company because client admin match failed',
			]
		);
	}

	employee.role = userRoles.MODULE_ADMIN;
	employee.moduleAdminAccess = Array.from(
		new Set([...employee.moduleAdminAccess, modules.HR])
	);
	await employee.save();
	res
		.status(StatusCodes.CREATED)
		.json(
			new APIResponse(
				StatusCodes.CREATED,
				'Module admin created successfully',
				employee
			)
		);
};

const deleteModuleAdmin = async (req, res) => {
	const { moduleAdminIds } = req.body;

	// console.log({ moduleAdminIds });

	const moduleAdmins = await User.find({
		_id: { $in: moduleAdminIds },
		role: userRoles.MODULE_ADMIN,
		deleted: false,
	});

	// console.log({ moduleAdmins });

	if (
		moduleAdmins.length === 0 ||
		moduleAdmins.length !== moduleAdminIds.length
	) {
		throw new NotFoundError('Module admin not found');
	}

	const updateModuleAccess = await User.updateMany(
		{
			_id: { $in: moduleAdminIds },
			deleted: false,
		},
		{
			$pull: { moduleAdminAccess: modules.HR },
		}
	);
	// console.log(` deleteModuleAdmin - updateModuleAccess:`, updateModuleAccess);

	const updateResult = await User.updateMany(
		{
			_id: { $in: moduleAdminIds },
			deleted: false,
			moduleAdminAccess: { $size: 0 },
		},
		{
			$set: {
				role: userRoles.EMPLOYEE,
				moduleAdminAccess: [],
			},
		}
	);

	// console.log({ updateResult });

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(
				StatusCodes.OK,
				`${updateResult.modifiedCount} module admin(s) updated successfully`
			)
		);
};

const getModuleAdmins = async (req, res) => {
	const page = parseInt(req.query.page) || 1;
	let limit = parseInt(req.query.limit) || 10;
	if (req.query.limit === 'all') {
		limit = await User.countDocuments({
			companyId: company._id,
			role: userRoles.MODULE_ADMIN,
		});
	}
	const options = { page, limit };
	// console.log(` getModuleAdmins - modules.HR:`, modules.HR);
	const moduleAdminAggregateQuery = User.aggregate(
		getModuleAdminsAggregation({
			companyId: req.user.companyId._id,
			module: modules.HR,
		})
	);

	const result = await User.aggregatePaginate(
		moduleAdminAggregateQuery,
		options
	);
	if (result.totalDocs === 0) {
		throw new NotFoundError('No module admins found');
	}

	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'Success', {
			moduleAdmins: result.docs,
			totalPages: result.totalPages,
			currentPage: result.page,
			totalDocs: result.totalDocs,
		})
	);
};

module.exports = {
	getModuleAdmins,
	createModuleAdmin,
	deleteModuleAdmin,
};
