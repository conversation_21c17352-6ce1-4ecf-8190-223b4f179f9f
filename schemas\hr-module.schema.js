const { z } = require('zod');

const createModuleAdminSchema = z
	.object({
		employeeId: z
			.string()
			.nonempty('Employee ID is required')
			.regex(/^[a-f\d]{24}$/, 'Invalid ID'),
	})
	.strict();

const deleteModuleAdminSchema = z
	.object({
		moduleAdminIds: z
			.array(
				z
					.string()
					.nonempty('Module admin ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid ID')
			)
			.nonempty('Module admin IDs are required'),
	})
	.strict();

module.exports = {
	createModuleAdminSchema,
	deleteModuleAdminSchema,
};
