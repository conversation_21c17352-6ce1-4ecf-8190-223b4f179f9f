const { StatusCodes } = require('http-status-codes');
const { logger } = require('../utils');
const errorHandlingMiddleware = (err, req, res, next) => {
	const customErrorObject = {
		message:
			err.message || 'Something went wrong, please try again after sometime',
		status: err.status || StatusCodes.INTERNAL_SERVER_ERROR,
	};
	if (err.code === 'LIMIT_UNEXPECTED_FILE') {
		customErrorObject.message = 'Too many files uploaded';
		customErrorObject.status = StatusCodes.BAD_REQUEST;
	}
	if (err.name === 'ValidationError') {
		customErrorObject.message = Object.values(err.errors)
			.map((item) => item.message)
			.join(', ');
		customErrorObject.status = StatusCodes.BAD_REQUEST;
	}
	if (err.code && err.code === 11000) {
		customErrorObject.message = `Duplicate value entered for  ${Object.keys(
			err.keyValue
		)} field please choose another value`;
		customErrorObject.status = StatusCodes.BAD_REQUEST;
	}
	if (err.name === 'CastError') {
		customErrorObject.message = `No item found with id  : ${err.value}`;
		customErrorObject.status = StatusCodes.NOT_FOUND;
	}
	logger.error(`Global Error: ${customErrorObject.message}`, {
		stack: err.stack,
		user: req.user ?? 'Application Error',
	});
	return res.status(customErrorObject.status).json({
		message: customErrorObject.message,
		errors: err,
		stack: process.env.NODE_ENV === 'development' ? err.stack : '',
	});
};

module.exports = errorHandlingMiddleware;
