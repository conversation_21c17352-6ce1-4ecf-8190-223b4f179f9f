const { StatusCodes } = require('http-status-codes');
const { NotFoundError, ConflictError, BadRequestError } = require('../errors');
const {
	CompanyDetails,
	Department,
	BusinessUnit,
	User,
	Designation,
} = require('../models');
const { APIResponse } = require('../utils');
const {
	getDepartmentsPipeline,
} = require('../db/aggregations/company-details.aggregation');

const addDepartment = async (req, res) => {
	const { departments } = req.body;

	const company = await CompanyDetails.findOne({
		_id: req.user.companyId._id,
	});
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const existingDepartments = await Department.find({
		companyId: company._id,
		name: { $in: departments.map((department) => department.name) },
		businessUnitId: {
			$in: departments.map((department) => department.businessUnitId),
		},
		deleted: false,
	});

	if (existingDepartments.length > 0) {
		throw new ConflictError('Departments already exist', existingDepartments);
	}

	const createdDepartments = [];

	for (const department of departments) {
		const businessUnit = await BusinessUnit.findById(department.businessUnitId);
		if (!businessUnit) {
			throw new NotFoundError('Business Unit not found', [
				`Business Unit with id :${department.businessUnitId} not found`,
			]);
		}
		const createdDepartment = await Department.create({
			name: department.name,
			businessUnitId: department.businessUnitId,
			admin: department.admin,
			companyId: company._id,
		});
		createdDepartments.push(createdDepartment._id);
	}

	res
		.status(StatusCodes.CREATED)
		.json(
			new APIResponse(
				StatusCodes.CREATED,
				'Departments added successfully',
				createdDepartments
			)
		);
};

const getAllDepartments = async (req, res) => {
	// const { clientAdminId } = req.user;
	const company = await CompanyDetails.findOne({
		_id: req.user.companyId._id,
		// owner: clientAdminId,
		// clientAdmin: clientAdminId,
		// deleted: false,
	});
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const page = parseInt(req.query.page) || 1;
	let limit = parseInt(req.query.limit) || 10;
	if (req.query.limit === 'all') {
		limit = await Department.countDocuments({ companyId: company._id });
	}
	const options = { page, limit };
	const departmentAggregateQuery = Department.aggregate(
		getDepartmentsPipeline(company._id)
	);
	const result = await Department.aggregatePaginate(
		departmentAggregateQuery,
		options
	);

	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'Success', {
			departments: result.docs,
			totalPages: result.totalPages,
			currentPage: result.page,
			totalResults: result.totalDocs,
		})
	);
};

const updateDepartment = async (req, res) => {
	const { _id } = req.body;

	const department = await Department.findOne({
		_id,
		deleted: false,
	});
	if (!department) {
		throw new NotFoundError('Department not found');
	}

	const user = await User.findOne({
		_id: req.body.admin,
		deleted: false,
		companyId: department.companyId,
		role: { $gt: 5 },
	});

	if (!user) {
		throw new NotFoundError('User not found to add as Department Head', [
			'User should be an employee of the company',
			'User should be an employee of that department',
		]);
	}

	await Department.updateOne({ _id }, { $set: req.body });

	const updatedDepartment = await Department.findById(_id);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(
				StatusCodes.OK,
				'Department updated successfully',
				updatedDepartment
			)
		);
};

const deleteDepartment = async (req, res) => {
	const { departmentIds } = req.body;

	if (!departmentIds) {
		throw new BadRequestError('Please provide departmentIds');
	}

	const departments = await Department.find({
		_id: { $in: departmentIds },
		deleted: false,
	});
	if (departments.length === 0) {
		throw new NotFoundError('Departments not found');
	}

	await Department.updateMany(
		{ _id: { $in: departmentIds } },
		{ $set: { deleted: true } }
	);

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Departments deleted successfully'));
};

const getDepartmentDesignations = async (req, res) => {
	const { id } = req.params;

	const department = await Department.findById(id).select('name');
	const designations = await Designation.find({
		departmentId: id,
		deleted: false,
	});

	if (designations.length === 0) {
		throw new NotFoundError(
			`Designations for department "${department.name}" not found`
		);
	}

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Success', designations));
};

module.exports = {
	getAllDepartments,
	addDepartment,
	updateDepartment,
	deleteDepartment,
	getDepartmentDesignations,
};
