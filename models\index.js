const { User } = require('./user.model');
const {
	EducationDetails,
} = require('./employee-detail-models/education-details.model');
const {
	EmploymentDetails,
} = require('./employee-detail-models/employment-details.model');
const { EditRequest } = require('./company-details-models/edit-request.model');
const UserFlags = require('./user-flags.model');
const Token = require('./token.model');
const CompanyDetails = require('./company-details-models/company-details.model');
const PersonalDetails = require('./employee-detail-models/personal-details.model');
const ExperienceDetails = require('./employee-detail-models/experience-details.model');
const FamilyDetails = require('./employee-detail-models/family-details.model');
const ContactDetails = require('./employee-detail-models/contact.model');
const EquipmentDetails = require('./employee-detail-models/equipment-issuance.model');
const Skills = require('./employee-detail-models/skills.model');
const Country = require('./country.model');
const Department = require('./company-details-models/department.model');
const BusinessUnit = require('./company-details-models/business-unit.model');
const Designation = require('./company-details-models/designation.model');
const Earnings = require('./employee-detail-models/earnings.model');
const Benefits = require('./employee-detail-models/employee-benefits.model');
const HolidayGroup = require('./employee-detail-models/holiday-group.model');
const Holiday = require('./employee-detail-models/holiday.model');
const Chat = require('./chat/chat.model');
const Message = require('./chat/message.model');
const Room = require('./chat/room.model');
const {
	ShiftSettings: Shift,
} = require('./attendance-module-models/shifts-setting.model');
const TimeLog = require('./attendance-module-models/time-log.model');
const QRSession = require('./qr-session.model');
const {
	Task,
	TaskGroup,
} = require('./projects-and-tasks-module-models/tasks.model');
const Project = require('./projects-and-tasks-module-models/projects.model');

module.exports = {
	Token,
	User,
	UserFlags,
	CompanyDetails,
	PersonalDetails,
	EducationDetails,
	Skills,
	ContactDetails,
	ExperienceDetails,
	FamilyDetails,
	EmploymentDetails,
	EquipmentDetails,
	Country,
	Department,
	BusinessUnit,
	Designation,
	Earnings,
	Benefits,
	HolidayGroup,
	Holiday,
	Chat,
	Message,
	Room,
	EditRequest,
	Shift,
	QRSession,
	TimeLog,
	Task,
	TaskGroup,
	Project,
};
