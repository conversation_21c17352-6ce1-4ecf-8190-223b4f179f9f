const { StatusCodes } = require('http-status-codes');
const jwt = require('jsonwebtoken');
const {
	ConflictError,
	NotFoundError,
	CustomError,
	BadRequestError,
	UnauthorizedError,
} = require('../errors');
const {
	PersonalDetails,
	CompanyDetails,
	EducationDetails,
	FamilyDetails,
	ExperienceDetails,
	ContactDetails,
	User,
	EmploymentDetails,
	EquipmentDetails,
	UserFlags,
	Benefits,
	Skills,
	Earnings,
	HolidayGroup,
	Token,
} = require('../models');
const {
	requiredFieldsErrorFunction,
	APIResponse,
	calculateAge,
	uploadFileToCloudinary,
	sendEmployeePasswordGenerationLinkEmail,
	convertToObjectId,
	checkPermissions,
	bulkImportEmployeesExcel,
	createHash,
	decryptToken,
	encryptToken,
	verifyToken,
	createTokenUser,
	createToken,
	getOrCreateRefreshToken,
	sendOnboardingLinkEmail,
} = require('../utils');
const { calculatePrStatus } = require('../utils/dateRelatedFunctions');
const {
	employeesListAggregation,
	getSingleEmployeeAggregation,
	getEmployeeHolidayGroups,
	getEmployeesByHolidayGroupIdAggregation,
	getEmployeeProfileDetailsAggregation,
} = require('../db/aggregations/employee.aggregation');
const { userRoles } = require('../models/user.model');
const crypto = require('crypto');
const dayjs = require('dayjs');

const createEmployeePersonalDetails = async (req, res) => {
	const existingUser = await User.findOne({
		email: req.body.email,
	});
	// console.log(` createEmployeePersonalDetails - req.body:`, req.body);
	const existingPersonalDetails = await PersonalDetails.findOne({
		userId: existingUser?._id,
	});

	if (
		existingUser &&
		existingPersonalDetails &&
		req.user.clientAdminId !== req.user.userId
	) {
		throw new ConflictError(
			'Personal Details already exists for this employee'
		);
	}

	const companyDetails = await CompanyDetails.findOne({
		owner: req.user.clientAdminId || req.user.userId,
	}).populate('owner');

	if (!companyDetails) {
		throw new NotFoundError('Company Details not found');
	}

	const ownerUserFlags = await UserFlags.findOne({
		userId: companyDetails.owner._id,
	});

	if (
		companyDetails.owner.email !== req.user.email &&
		ownerUserFlags.isClientRegistrationAsEmployeeComplete === false
	) {
		throw new BadRequestError(
			'Please complete client registration as employee first'
		);
	}

	const profilePhotoUrl = req.file
		? await uploadFileToCloudinary(req.file.path)
		: undefined;

	const personalDetails = await PersonalDetails.create({
		...req.body,
		profilePhoto: profilePhotoUrl,
		prStatus:
			req.body.residentialStatus === 'Singapore Citizen'
				? 'singaporean'
				: calculatePrStatus(req.body.issueDate),
	});

	let employee;
	if (
		(req.user.role === userRoles.CLIENT_ADMIN ||
			req.user.role === userRoles.GLORIFIED_CLIENT_ADMIN) &&
		ownerUserFlags.isClientRegistrationAsEmployeeComplete === false
	) {
		employee = await User.findById(req.user.userId);
	} else {
		employee = await User.create({
			companyId: companyDetails._id,
			clientAdminId: req.user.clientAdminId,
			...req.body,
		});
	}

	employee.name = personalDetails.nameOnNRIC;
	employee.profilePhoto = personalDetails.profilePhoto;
	await employee.save();

	personalDetails.userId = employee._id;
	await personalDetails.save();
	const familyDetails = await FamilyDetails.create({
		...req.body,
		userId: employee._id,
	});

	await UserFlags.findOneAndUpdate(
		{ userId: employee._id },
		{
			$set: {
				isPersonalDetailsComplete: !!personalDetails,
				isFamilyDetailsComplete: !!familyDetails,
			},
		},
		{
			upsert: true,
		}
	);

	res.status(StatusCodes.CREATED).json(
		new APIResponse(
			StatusCodes.CREATED,
			'Personal Details Submitted Successfully',
			{
				employee: {
					employeeId: employee._id,
					employeeClientId: employee.clientAdminId,
					employeeCompanyId: employee.companyId,
					name: personalDetails.nameOnNRIC,
					employeeOrgId: personalDetails.employeeOrgId,
				},
				personalDetails,
				familyDetails,
			}
		)
	);
};

const addEducationAndSkillsDetails = async (req, res) => {
	const {
		employeeId,
		educationalDetails,
		hardSkills,
		softSkills,
		experienceDetails,
	} = req.body;

	// Validate Employee ID
	const employee = await User.findById(employeeId);
	if (!employee) {
		throw new NotFoundError('Employee not found');
	}

	const uploadedFiles = req.files;

	let savedEducation = [];
	let savedExperience = [];
	if (educationalDetails?.length > 0) {
		for (let i = 0; i < educationalDetails.length; i++) {
			const education = educationalDetails[i];
			let documentUrl = null;
			if (uploadedFiles && uploadedFiles[i]) {
				documentUrl = await uploadFileToCloudinary(uploadedFiles[i].path);
			}

			const newEducation = await EducationDetails.create({
				userId: employee._id,
				document: documentUrl,
				...education,
			});
			savedEducation.push(newEducation._id);
		}
	}

	if (experienceDetails?.length > 0) {
		for (let i = 0; i < experienceDetails.length; i++) {
			const experience = experienceDetails[i];
			let documentUrl = null;
			if (uploadedFiles && uploadedFiles[i + educationalDetails.length]) {
				documentUrl = await uploadFileToCloudinary(
					uploadedFiles[i + educationalDetails.length].path
				);
			}

			const newExperience = await ExperienceDetails.create({
				userId: employee._id,
				document: documentUrl,
				...experience,
			});
			savedExperience.push(newExperience._id);
		}
	}

	const newSkills = await Skills.create({
		userId: employee._id,
		hardSkills,
		softSkills,
	});

	await UserFlags.updateOne(
		{
			userId: employee._id,
		},
		{
			$set: {
				isQualificationDetailsComplete: savedEducation.length > 0,
				isSkillsDetailsComplete: !!newSkills,
			},
		}
	);

	res.status(StatusCodes.CREATED).json(
		new APIResponse(
			StatusCodes.CREATED,
			'Education & Experience added successfully',
			{
				education: savedEducation,
				experience: savedExperience,
				skills: newSkills._id,
			}
		)
	);
};

const addContactDetails = async (req, res) => {
	const { employeeId, contacts } = req.body;

	const employee = await User.findById(employeeId);

	if (!employee) {
		throw new BadRequestError('Employee not found');
	}

	const newContacts = [];
	for (const contact of contacts) {
		const newContactDetails = await ContactDetails.create({
			userId: employee._id,
			...contact,
		});
		newContacts.push(newContactDetails._id);
	}

	await UserFlags.updateOne(
		{
			userId: employee._id,
		},
		{
			$set: {
				isContactDetailsComplete: newContacts.length > 0,
			},
		}
	);

	res
		.status(StatusCodes.CREATED)
		.json(new APIResponse(StatusCodes.CREATED, 'Contact Details Added'));
};

const addEmploymentDetails = async (req, res) => {
	const { employeeId, businessUnit, department, designation } = req.body;
	const existingEmployee = await User.findOne({ _id: employeeId });
	const existingEmploymentDetails = await EmploymentDetails.findOne({
		userId: existingEmployee._id,
	});

	if (existingEmploymentDetails) {
		throw new BadRequestError('Employment details already exists');
	}

	if (req.body.equipment.length > 0) {
		for (const equipment of req.body.equipment) {
			await EquipmentDetails.create({
				...equipment,
				userId: existingEmployee._id,
			});
		}
	}

	const employmentDetails = await EmploymentDetails.create({
		...req.body,
		userId: existingEmployee._id,
	});
	existingEmployee.isBlocked = req.body.isBlocked;
	existingEmployee.role = parseInt(req.body.employeeRole);

	const assignedHolidayGroups = await HolidayGroup.aggregate(
		getEmployeeHolidayGroups(businessUnit, department, designation)
	);
	await Benefits.create({
		userId: existingEmployee._id,
		holidayGroups: assignedHolidayGroups[0].holidayGroupIds,
	});

	await existingEmployee.save();
	await UserFlags.findOneAndUpdate(
		{
			userId: existingEmployee._id,
		},
		{
			$set: { isEmploymentDetailsComplete: true },
		}
	);

	res
		.status(StatusCodes.CREATED)
		.json(new APIResponse(StatusCodes.CREATED, 'Employment Details created'));
};

const addEarningsDetails = async (req, res) => {
	const { employeeId } = req.body;
	const existingEmployee = await User.findOne({ _id: employeeId });
	const existingEarningsDetails = await Earnings.findOne({
		userId: existingEmployee?._id,
	});

	if (existingEarningsDetails) {
		throw new BadRequestError('Earnings details already exists');
	}

	const earningsDetails = await Earnings.create({
		...req.body,
		userId: existingEmployee._id,
	});

	const userFlag = await UserFlags.findOne({
		userId: existingEmployee._id,
	});
	userFlag.isEarningsDetailsComplete = true;
	userFlag.isClientRegistrationAsEmployeeComplete = true;
	userFlag.isRegistrationComplete = true;
	existingEmployee.isHired = true;
	existingEmployee.isVerified = true;
	existingEmployee.verified = new Date(Date.now());
	await existingEmployee.save();
	await userFlag.save();

	// NOTE: Previously we were only completing the emp onboarding in earnings form if they were client admin or gClient admin but now we are completing registration for all the employees, we have left this code as commented for future purpose
	// if (
	// 	existingEmployee.role === userRoles.CLIENT_ADMIN &&
	// 	existingEmployee.role === userRoles.GLORIFIED_CLIENT_ADMIN &&
	// 	userFlag.isClientRegistrationAsEmployeeComplete === false
	// ) {
	// 	userFlag.isClientRegistrationAsEmployeeComplete = true;
	// 	userFlag.isRegistrationComplete === true;
	// 	await existingEmployee.save();
	// 	await userFlag.save();
	// }

	if (
		![userRoles.GLORIFIED_CLIENT_ADMIN, userRoles.CLIENT_ADMIN].includes(
			existingEmployee.role
		)
	) {
		const token = crypto.randomBytes(70).toString('hex');
		const origin =
			process.env.NODE_ENV === 'production'
				? process.env.FRONTEND_ORIGIN
				: process.env.DEVELOPMENT_ORIGIN;
		const email = existingEmployee.email;
		const expirationTime = dayjs().add(3, 'day');
		const companyName = existingEmployee.companyId?.businessName;
		await sendEmployeePasswordGenerationLinkEmail({
			origin,
			email,
			token,
			expirationTime,
			companyName,
		});
		existingEmployee.resetPasswordToken = createHash(token);
		existingEmployee.resetPasswordTokenExpiry = expirationTime;
		await existingEmployee.save();
	}
	res.status(StatusCodes.CREATED).json(
		new APIResponse(
			StatusCodes.CREATED,
			"Employee onboarded successfully, password generation link sent to employee's email"
			// earningsDetails
		)
	);
};

const getAllEmployees = async (req, res) => {
	const page = parseInt(req.query.page) || 1;
	const limit = parseInt(req.query.limit) || 10;

	const clientAdminId = req.user.clientAdminId;
	const employeesAggregateQuery = User.aggregate(
		employeesListAggregation({
			id: clientAdminId,
			companyId: req.user.companyId._id,
		})
	);

	const options = { page, limit };
	const result = await User.aggregatePaginate(employeesAggregateQuery, options);
	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'Success', {
			employees: result.docs,
			totalPages: result.totalPages,
			currentPage: result.page,
			totalResults: result.totalDocs,
		})
	);
};

const getSingleEmployee = async (req, res) => {
	const { id } = req.params;
	const employee = await User.aggregate(
		getSingleEmployeeAggregation({
			clientAdminId: req.user.clientAdminId,
			userId: id,
		})
	);

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Success', employee[0]));
};

const updatePersonalDetails = async (req, res) => {
	const { employeeId, email } = req.body;
	const personalDetails = req.body;
	const existingPersonalDetails = await PersonalDetails.findOne({
		userId: { $ne: convertToObjectId(employeeId) },
		email,
	});
	if (existingPersonalDetails) {
		throw new NotFoundError('Employee with same email already exists');
	}

	await PersonalDetails.findOneAndUpdate(
		{ userId: convertToObjectId(employeeId) },
		{ $set: personalDetails }
	);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, 'Personal Details updated successfully')
		);
};

const updateFamilyDetails = async (req, res) => {
	const { employeeId } = req.body;
	const familyDetails = req.body;
	await FamilyDetails.findOneAndUpdate(
		{ userId: convertToObjectId(employeeId) },
		{ $set: familyDetails }
	);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, 'Family Details updated successfully')
		);
};

const updateEducationDetails = async (req, res) => {
	const {
		employeeId,
		education: educationDetails,
		deletedEducation,
		skills,
	} = req.body;

	const bulkOps = [];
	educationDetails.forEach((education) => {
		if (education._id) {
			bulkOps.push({
				updateOne: {
					filter: { _id: convertToObjectId(education._id) },
					update: { $set: education },
				},
			});
		} else {
			bulkOps.push({
				insertOne: {
					document: {
						userId: convertToObjectId(employeeId),
						...education,
					},
				},
			});
		}
	});
	deletedEducation.forEach((educationId) => {
		bulkOps.push({
			deleteOne: {
				filter: { _id: convertToObjectId(educationId) },
			},
		});
	});
	await EducationDetails.bulkWrite(bulkOps);

	await Skills.findOneAndUpdate(
		{ userId: convertToObjectId(employeeId) },
		{
			$set: {
				hardSkills: skills.hardSkills,
				softSkills: skills.softSkills,
			},
		}
	);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, 'Education Details updated successfully')
		);
};

const updateExperienceDetails = async (req, res) => {
	const {
		employeeId,
		experience: experienceDetails,
		deletedExperience,
	} = req.body;

	const bulkOps = [];
	experienceDetails.forEach((experience) => {
		if (experience._id) {
			bulkOps.push({
				updateOne: {
					filter: { _id: convertToObjectId(experience._id) },
					update: { $set: experience },
				},
			});
		} else {
			bulkOps.push({
				insertOne: {
					document: {
						userId: convertToObjectId(employeeId),
						...experience,
					},
				},
			});
		}
	});
	deletedExperience.forEach((experienceId) => {
		bulkOps.push({
			deleteOne: {
				filter: { _id: convertToObjectId(experienceId) },
			},
		});
	});
	await ExperienceDetails.bulkWrite(bulkOps);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, 'Experience Details updated successfully')
		);
};

const updateContactDetails = async (req, res) => {
	const { employeeId, contacts, deletedContacts } = req.body;

	const bulkOps = [];
	contacts.forEach((contact) => {
		if (contact._id) {
			bulkOps.push({
				updateOne: {
					filter: { _id: convertToObjectId(contact._id) },
					update: { $set: contact },
				},
			});
		} else {
			bulkOps.push({
				insertOne: {
					document: {
						userId: convertToObjectId(employeeId),
						...contact,
					},
				},
			});
		}
	});
	deletedContacts.forEach((contactId) => {
		bulkOps.push({
			deleteOne: {
				filter: { _id: convertToObjectId(contactId) },
			},
		});
	});
	await ContactDetails.bulkWrite(bulkOps);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, 'Contact Details updated successfully')
		);
};

const updateEmploymentDetails = async (req, res) => {
	const { employeeId } = req.body;
	const employmentDetails = req.body;
	await EmploymentDetails.findOneAndUpdate(
		{ userId: convertToObjectId(employeeId) },
		{ $set: employmentDetails }
	);
	await User.findOneAndUpdate(
		{ _id: convertToObjectId(employeeId) },
		{ $set: { isBlocked: employmentDetails.isBlocked } }
	);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, 'Employment Details updated successfully')
		);
};

const updateEarningsDetails = async (req, res) => {
	const { employeeId } = req.body;
	const earningsDetails = req.body;
	await Earnings.findOneAndUpdate(
		{ userId: convertToObjectId(employeeId) },
		{ $set: earningsDetails }
	);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, 'Earnings Details updated successfully')
		);
};

const updateBenefitDetails = async (req, res) => {
	const { employeeId } = req.body;
	const benefitDetails = req.body;
	await Benefits.findOneAndUpdate(
		{ userId: convertToObjectId(employeeId) },
		{ $set: benefitDetails }
	);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, 'Benefit Details updated successfully')
		);
};

const getEmployeesByHolidayGroupId = async (req, res) => {
	const { holidayGroupId } = req.params;
	const employees = await Benefits.aggregate(
		getEmployeesByHolidayGroupIdAggregation(holidayGroupId)
	);
	// const employees = await Benefits.find({
	// 	holidayGroups: convertToObjectId(holidayGroupId),
	// });

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Success', employees));
};

const removeEmployeeFromHolidayGroup = async (req, res) => {
	const { holidayGroupId, employeeId } = req.params;

	await Benefits.findOneAndUpdate(
		{ userId: convertToObjectId(employeeId) },
		{ $pull: { holidayGroups: convertToObjectId(holidayGroupId) } }
	);

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Employee removed successfully'));
};

const getEmployeeProfileDetails = async (req, res) => {
	const { employeeId } = req.params;

	const employee = await User.aggregate(
		getEmployeeProfileDetailsAggregation(employeeId)
	);

	if (employee.length === 0) {
		throw new NotFoundError('Employee not found');
	}
	checkPermissions(req.user, employee[0]._id);

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Success', employee[0]));
};

const bulkEmployeeOnboarding = async (req, res) => {
	const filepath = req.file.path;
	const data = bulkImportEmployeesExcel(filepath);
	res.send('bulk onboarding');
};

const onboardEmployeeUsingLink = async (req, res) => {
	const { employeeDetails } = req.body;
	const users = await User.insertMany(employeeDetails, { ordered: false });
	const origin =
		process.env.NODE_ENV === 'production'
			? process.env.FRONTEND_ORIGIN
			: process.env.STAGING_ORIGIN;
	for (const user of users) {
		const tokenUser = createTokenUser(user);
		const token = createToken({ payload: tokenUser });
		const encryptedToken = encryptToken(token);
		await sendOnboardingLinkEmail({
			name: user.name,
			email: user.email,
			token: encryptedToken,
			origin,
			companyName: req.user.companyId.businessName,
		});
	}

	res
		.status(StatusCodes.CREATED)
		.json(
			new APIResponse(
				StatusCodes.CREATED,
				'Employees created successfully',
				users
			)
		);
};

const verifyLinkOnboardRequest = async (req, res) => {
	const { token } = req.params;
	const decryptedToken = decryptToken(token);
	const payload = verifyToken(decryptedToken);
	if (!payload) {
		throw new UnauthorizedError('Unauthorized Access');
	}
	const employee = await User.aggregate([
		{
			$match: {
				_id: convertToObjectId(payload.userId),
			},
		},
		{
			$lookup: {
				from: 'userflags',
				localField: '_id',
				foreignField: 'userId',
				as: 'flags',
			},
		},
		{
			$addFields: {
				flags: { $first: '$flags' },
			},
		},
		{
			$project: {
				name: 1,
				email: 1,
				isPersonalDetailsComplete: '$flags.isPersonalDetailsComplete',
				isContactDetailsComplete: '$flags.isContactDetailsComplete',
				isFamilyDetailsComplete: '$flags.isFamilyDetailsComplete',
				isQualificationDetailsComplete: '$flags.isQualificationDetailsComplete',
				isRegistrationComplete: '$flags.isRegistrationComplete',
				isSkillsDetailsComplete: '$flags.isSkillsDetailsComplete',
			},
		},
	]);
	if (!employee) {
		throw new NotFoundError('Employee not found');
	}
	await getOrCreateRefreshToken({ req, res, user: employee });
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Success', employee[0]));
};

module.exports = {
	createEmployeePersonalDetails,
	addEducationAndSkillsDetails,
	addContactDetails,
	addEmploymentDetails,
	addEarningsDetails,
	getSingleEmployee,
	updatePersonalDetails,
	updateFamilyDetails,
	updateEducationDetails,
	updateExperienceDetails,
	updateContactDetails,
	updateEmploymentDetails,
	updateEarningsDetails,
	updateBenefitDetails,
	getEmployeesByHolidayGroupId,
	removeEmployeeFromHolidayGroup,
	getAllEmployees,
	getEmployeeProfileDetails,
	bulkEmployeeOnboarding,
	onboardEmployeeUsingLink,
	verifyLinkOnboardRequest,
};
