const { default: mongoose } = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

const SkillsSchema = new mongoose.Schema({
	userId: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'User',
	},
	hardSkills: [
		{
			type: String,
		},
	],
	softSkills: [
		{
			type: String,
		},
	],
});

SkillsSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
SkillsSchema.plugin(aggregatePaginate);
SkillsSchema.plugin(autoPopulate);

module.exports = mongoose.model('Skills', SkillsSchema);
