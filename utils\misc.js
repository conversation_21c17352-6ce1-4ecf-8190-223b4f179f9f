const { mongoose } = require('mongoose');

const convertToObjectId = (id) => {
	if (Array.isArray(id)) {
		return id.map((id) => new mongoose.Types.ObjectId(id));
	}
	return new mongoose.Types.ObjectId(id);
};

const areValidObjectIds = (ids) =>
	Array.isArray(ids) && ids.every((id) => mongoose.Types.ObjectId.isValid(id));

const getClientIp = (req) => {
	let ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress || '';
	ip = ip.split(',')[0].trim();

	if (ip.startsWith('::ffff:')) {
		ip = ip.replace('::ffff:', '');
	}

	return ip;
};

module.exports = { convertToObjectId, areValidObjectIds, getClientIp };
