const { StatusCodes } = require('http-status-codes');
const {
	getHolidayPipeline,
} = require('../db/aggregations/company-details.aggregation');
const { NotFoundError, ConflictError, BadRequestError } = require('../errors');
const { CompanyDetails, Holiday } = require('../models');
const { APIResponse, convertToObjectId } = require('../utils');
const dayjs = require('dayjs');

const getHolidays = async (req, res) => {
	const company = await CompanyDetails.findOne({ _id: req.user.companyId._id });
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const page = parseInt(req.query.page) || 1;
	const limit = parseInt(req.query.limit) || 10;
	const options = { page, limit };
	const holidayAggregateQuery = Holiday.aggregate(
		getHolidayPipeline(company._id)
	);
	const result = await Holiday.aggregatePaginate(
		holidayAggregateQuery,
		options
	);

	if (result.docs === 0) {
		throw new NotFoundError('Holidays not found');
	}

	res.status(StatusCodes.OK);
	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'Success', {
			holidays: result.docs,
			totalPages: result.totalPages,
			currentPage: result.page,
			totalResults: result.totalDocs,
		})
	);
};

const getHolidaysByHolidayGroupId = async (req, res) => {
	const { holidayGroupId } = req.params;
	const holidays = await Holiday.find({ holidayGroupId, deleted: false });

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Success', holidays));
};

const addHoliday = async (req, res) => {
	const { title, holidayGroupId, startDate, endDate } = req.body;

	const company = await CompanyDetails.findOne({ _id: req.user.companyId._id });
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const existingHoliday = await Holiday.findOne({
		companyId: company._id,
		title,
		holidayGroupId,
		deleted: false,
	});
	if (existingHoliday) {
		throw new ConflictError('Holiday already exist', existingHoliday);
	}

	let numberOfDays = 1;
	if (startDate && endDate) {
		numberOfDays = dayjs(endDate).diff(dayjs(startDate), 'day') + 1;
	}

	const holiday = await Holiday.create({
		...req.body,
		numberOfDays,
		endDate: endDate ? endDate : null,
		companyId: company._id,
	});

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, 'Holiday added successfully', holiday)
		);
};

const updateHoliday = async (req, res) => {
	const { _id, title, holidayGroupId, startDate, endDate } = req.body;

	const company = await CompanyDetails.findOne({ _id: req.user.companyId._id });
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const existingHoliday = await Holiday.findOne({
		_id: { $ne: convertToObjectId(_id) },
		companyId: company._id,
		title,
		holidayGroupId,
		deleted: false,
	});
	if (existingHoliday) {
		throw new ConflictError('Holiday already exist', existingHoliday);
	}

	let numberOfDays = 1;
	if (startDate && endDate) {
		numberOfDays = dayjs(endDate).diff(dayjs(startDate), 'day') + 1;
	}

	const holiday = await Holiday.findOneAndUpdate(
		{ _id: convertToObjectId(_id) },
		{
			...req.body,
			numberOfDays,
			endDate: endDate ? endDate : null,
			companyId: company._id,
		}
	);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, 'Holiday updated successfully', holiday)
		);
};

const deleteHoliday = async (req, res) => {
	const { holidayIds } = req.body;

	if (!holidayIds) {
		throw new BadRequestError('Please provide Holiday Ids');
	}

	const company = await CompanyDetails.findOne({ _id: req.user.companyId._id });
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const holiday = await Holiday.findOne({
		_id: { $in: convertToObjectId(holidayIds) },
		companyId: company._id,
		deleted: false,
	});
	if (!holiday) {
		throw new NotFoundError('Holidays not found');
	}

	await Holiday.updateMany(
		{ _id: { $in: holidayIds } },
		{ $set: { deleted: true, deletedAt: new Date() } }
	);

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Holidays deleted successfully'));
};

module.exports = {
	getHolidays,
	addHoliday,
	updateHoliday,
	getHolidaysByHolidayGroupId,
	deleteHoliday,
};
