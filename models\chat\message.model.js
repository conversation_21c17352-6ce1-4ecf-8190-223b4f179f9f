const mongoose = require('mongoose');
const autoPopulate = require('mongoose-autopopulate');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const MessageSchema = new mongoose.Schema(
	{
		sender: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
			required: true,
		},
		// receiver: {
		// 	type: mongoose.Schema.Types.ObjectId,
		// 	ref: 'User',
		// 	required: true,
		// },
		chat: { type: mongoose.Schema.Types.ObjectId, ref: 'Chat' }, // for one-to-one chats
		room: { type: mongoose.Schema.Types.ObjectId, ref: 'Room' }, // for group chats
		content: { type: String, required: true },
		type: { type: String, default: 'text' }, // e.g., text, image, video, etc.
		isRead: { type: Boolean, default: false },
	},
	{ timestamps: true }
);

MessageSchema.plugin(autoPopulate);
MessageSchema.plugin(aggregatePaginate);
MessageSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});

module.exports = mongoose.model('Message', MessageSchema);
