const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

const BenefitsSchema = new mongoose.Schema(
	{
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
		eligibleForOffInLieu: {
			type: Boolean,
			default: false,
		},
		holidayGroups: [
			{
				type: mongoose.Schema.Types.ObjectId,
				ref: 'HolidayGroup',
			},
		],
		leaveGroups: [
			{
				type: mongoose.Schema.Types.ObjectId,
				ref: 'LeaveGroup',
			},
		],
	},
	{ timestamps: true }
);

BenefitsSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
BenefitsSchema.plugin(aggregatePaginate);
BenefitsSchema.plugin(autoPopulate);

module.exports = mongoose.model('Benefits', BenefitsSchema);
