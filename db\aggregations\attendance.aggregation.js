const { convertToObjectId } = require('../../utils');

const getEmployeeTimeSheetAggregation = ({
	startDate,
	endDate,
	companyId,
	allDays,
}) => {
	return [
		{
			$match: {
				companyId: convertToObjectId(companyId),
				deleted: false,
			},
		},
		{
			$lookup: {
				from: 'timelogs',
				let: { userId: '$_id' },
				pipeline: [
					{
						$match: {
							$expr: {
								$and: [
									{ $eq: ['$userId', '$$userId'] },
									{ $gte: ['$createdAt', startDate] },
									{ $lt: ['$createdAt', endDate] },
								],
							},
						},
					},
					{
						$addFields: {
							logDate: {
								$dateToString: { format: '%Y-%m-%d', date: '$createdAt' },
							},
						},
					},
				],
				as: 'timeLogs',
			},
		},
		{
			$project: {
				name: 1,
				timeLogs: 1,
				presencePerDay: {
					$map: {
						input: allDays,
						as: 'day',
						in: {
							date: '$$day',
							present: {
								$in: ['$$day', '$timeLogs.logDate'],
							},
						},
					},
				},
			},
		},
		{
			$project: {
				name: 1,
				presencePerDay: 1,
			},
		},
	];
};

const getTimeSheetForEmployee = ({ startDate, endDate, userId, allDays }) => {
	return [
		{
			$match: {
				_id: convertToObjectId(userId),
				deleted: false,
			},
		},
		{
			$lookup: {
				from: 'timelogs',
				let: { userId: '$_id' },
				pipeline: [
					{
						$match: {
							$expr: {
								$and: [
									{ $eq: ['$userId', '$$userId'] },
									{ $gte: ['$createdAt', startDate] },
									{ $lt: ['$createdAt', endDate] },
								],
							},
						},
					},
					{
						$addFields: {
							logDate: {
								$dateToString: { format: '%Y-%m-%d', date: '$createdAt' },
							},
						},
					},
				],
				as: 'timeLogs',
			},
		},
		{
			$project: {
				name: 1,
				presenceSheet: {
					$map: {
						input: allDays,
						as: 'day',
						in: {
							date: '$$day',
							present: {
								$in: ['$$day', '$timeLogs.logDate'],
							},
						},
					},
				},
			},
		},
	];
};

module.exports = {
	getEmployeeTimeSheetAggregation,
	getTimeSheetForEmployee,
};
