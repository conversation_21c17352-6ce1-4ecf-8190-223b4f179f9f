const express = require('express');
const { authenticationMiddleware } = require('../middlewares');
const {
	employeeClockIn,
	startBreak,
	clockOut,
	endBreak,
	getCurrentAttendanceStatus,
	getTimeLogs,
	getTimeSheet,
} = require('../controllers/attendance-module/attendance.controller');
const router = express.Router();

router.route('/logs').get(authenticationMiddleware, getTimeLogs);
router
	.route('/status')
	.get(authenticationMiddleware, getCurrentAttendanceStatus);
router.route('/clock-in').post(authenticationMiddleware, employeeClockIn);
router.route('/start-break').patch(authenticationMiddleware, startBreak);
router.route('/end-break').patch(authenticationMiddleware, endBreak);
router.route('/clock-out').patch(authenticationMiddleware, clockOut);
router.route('/time-sheet').get(authenticationMiddleware, getTimeSheet);

module.exports = router;
