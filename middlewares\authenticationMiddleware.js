const dayjs = require('dayjs');
const { UnauthorizedError, BadRequestError } = require('../errors');
const { Token, QRSession } = require('../models');
const {
	loginSchema,
	registerEmailSchema,
	verifyEmailSchema,
	registerSchema,
	updatePasswordSchema,
	forgotPasswordSchema,
	mobileLoginSchema,
} = require('../schemas/auth.schema');
const {
	verifyToken,
	attachCookiesToResponse,
	decryptToken,
	isMobileRequest,
	logger,
} = require('../utils');
const { validateEmail } = require('./emailValidationMiddleware');
const { sessionMap } = require('../sockets/namespaces/auth/sessionMap');

const authenticationMiddleware = async (req, res, next) => {
	let accessToken = null;
	let refreshToken = null;

	const isMobile = isMobileRequest(req);

	if (isMobile === false) {
		// Extract token from signed cookies
		accessToken = req.signedCookies['accessToken'];
		refreshToken = req.signedCookies['refreshToken'];
	} else {
		// Extract token from headers
		const authHeader = req.headers['authorization'];

		// Adding this log to check the headers
		// console.log(
		// 	`authenticationMiddleware - req.headers['authorization']: ${req.headers['authorization']}, authenticationMiddleware - req.headers.authorization: ${req.headers.authorization}, authenticationMiddleware - authHeader: ${authHeader}, authenticationMiddleware - req.headers['x-refresh-token']: ${req.headers['x-refresh-token']}`
		// );

		if (!authHeader && !authHeader.startsWith('Bearer ')) {
			throw new UnauthorizedError('Authentication Invalid', [
				'Access Token not found in authorization header',
				'Authorization header not found',
			]);
		}
		accessToken = authHeader.split(' ')[1];
		refreshToken = req.headers['x-refresh-token'];
	}

	try {
		// if (!refreshToken) {
		// 	throw new UnauthorizedError("No refresh token found");
		// }
		if (accessToken) {
			const decryptedAccessToken = decryptToken(accessToken);
			if (!decryptedAccessToken) {
				throw new UnauthorizedError('Authentication Invalid', [
					'Invalid Access token',
				]);
			}
			const payload = verifyToken(decryptedAccessToken);
			if (!payload) {
				throw new UnauthorizedError('Authentication Invalid', [
					'Access Token Verification Failed',
				]);
			}
			// this was throwing error if user logged out in any other device so for now we have commented it out
			// if (!(await Token.findOne({ user: payload.userId }))) {
			// 	throw new UnauthorizedError('Authentication Invalid', [
			// 		'No refresh token in db',
			// 	]);
			// }
			req.user = payload;
			return next();
		}

		if (!refreshToken) {
			res.clearCookie('accessToken');
			res.clearCookie('refreshToken');
			throw new UnauthorizedError('No refresh token found', [
				'No refresh token found',
			]);
		}
		const decryptedRefreshToken = decryptToken(refreshToken);
		if (!decryptedRefreshToken) {
			res.clearCookie('accessToken');
			res.clearCookie('refreshToken');
			throw new UnauthorizedError('Invalid refresh token', [
				'Invalid refresh token',
			]);
		}
		const payload = verifyToken(decryptedRefreshToken);
		const existingToken = await Token.findOne({
			user: payload.tokenUser.userId,
			refreshToken: payload.refreshToken,
		});

		if (!existingToken || !existingToken.isValid) {
			res.clearCookie('accessToken');
			res.clearCookie('refreshToken');
			throw new UnauthorizedError('Contact Admin', [
				'Existing token not found or Token is not valid',
			]);
		}

		const tokenUser = payload.tokenUser;

		attachCookiesToResponse({
			res,
			isMobile,
			user: tokenUser,
			refreshToken: payload.refreshToken,
		});

		req.user = tokenUser;
		next();
	} catch (error) {
		res.clearCookie('accessToken');
		res.clearCookie('refreshToken');
		throw new UnauthorizedError('Authentication Invalid', error.errors);
	}
};

// Inside your existing Express auth middleware, after user is authenticated

const qrMiddleware = async (req, res, next) => {
	try {
		const { qrSessionId } = req.body;
		if (!qrSessionId) {
			throw new BadRequestError('QR session ID is required');
		}

		const socketId = sessionMap.get(qrSessionId);

		if (!socketId) {
			throw new BadRequestError('QR session not found');
		}

		// Attach qrSession to req for next handlers
		req.qrSession = socketId;

		next();
	} catch (err) {
		console.error('QR Middleware error:', err);
		res.status(500).json({ error: 'Internal server error' });
	}
};

const authorizePermissions = (...roles) => {
	return (req, res, next) => {
		if (!roles.includes(req.user.role)) {
			throw new UnauthorizedError(
				'You are not authorized to access this route'
			);
		}
		next();
	};
};

const loginMiddleware = (req, res, next) => {
	const result = loginSchema.safeParse(req.body);
	if (!result.success) {
		throw new UnauthorizedError('Invalid Credentials', result.error.format());
	}
	next();
};
const mobileLoginMiddleware = (req, res, next) => {
	const isMobile = isMobileRequest(req);
	if (!isMobile) {
		throw new UnauthorizedError('Something went wrong', [
			'Request is not from mobile',
		]);
	}
	const result = mobileLoginSchema.safeParse(req.body);
	if (!result.success) {
		throw new UnauthorizedError('Invalid Credentials', result.error.format());
	}
	next();
};

const registerEmailMiddleware = async (req, res, next) => {
	// // console.log(`registerEmailMiddleware - req:`, req.body);
	const result = registerEmailSchema.safeParse(req.body);
	// // console.log(`registerEmailMiddleware - result:`, result);
	if (!result.success) {
		throw new BadRequestError('Invalid Email', result.error.format());
	}
	const isTempEmail = await validateEmail(result.data.email);
	if (isTempEmail) {
		throw new BadRequestError('Temporary emails are not allowed');
	}
	next();
};

const verifyEmailMiddleware = (req, res, next) => {
	const result = verifyEmailSchema.safeParse(req.body);
	if (!result.success) {
		throw new UnauthorizedError(
			'Email Verification Failed',
			result.error.format()
		);
	}
	next();
};

const registerMiddleware = async (req, res, next) => {
	const result = registerSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Registration Failed', result.error.format());
	}
	next();
};

const forgotPasswordMiddleware = async (req, res, next) => {
	const result = forgotPasswordSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Password Reset Failed', result.error.format());
	}
	next();
};

const updatePasswordMiddleware = async (req, res, next) => {
	// console.log(` updatePasswordMiddleware - req:`, req.body.token.length);

	const result = updatePasswordSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Password Update Failed', result.error.format());
	}
	next();
};

module.exports = {
	authenticationMiddleware,
	authorizePermissions,
	loginMiddleware,
	registerEmailMiddleware,
	verifyEmailMiddleware,
	registerMiddleware,
	updatePasswordMiddleware,
	forgotPasswordMiddleware,
	mobileLoginMiddleware,
	qrMiddleware,
};
