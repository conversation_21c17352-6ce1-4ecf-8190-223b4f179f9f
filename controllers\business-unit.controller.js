const { StatusCodes } = require('http-status-codes');
const { CompanyDetails, BusinessUnit, User, Department } = require('../models');
const { APIResponse } = require('../utils');
const { NotFoundError } = require('../errors');
const {
	getBusinessUnitsPipeline,
	getPopulatedBusinessUnitsPipeline,
} = require('../db/aggregations/company-details.aggregation');

const addBusinessUnit = async (req, res) => {
	const { businessUnits } = req.body;

	const companyDetails = await CompanyDetails.findOne({
		_id: req.user.companyId._id,
	});

	const businessUnitData = [];

	for (const businessUnit of businessUnits) {
		const { name, location, admin } = businessUnit;
		const existingBusinessUnit = await BusinessUnit.findOne({
			name,
			companyId: companyDetails._id,
			location,
			admin,
		});
		if (existingBusinessUnit) {
			continue;
		}
		const newBusinessUnit = await BusinessUnit.create({
			name,
			location,
			admin,
			companyId: companyDetails._id,
			owner: companyDetails.owner,
		});
		businessUnitData.push(newBusinessUnit._id);
	}

	// companyDetails.businessUnits = [...companyDetails.businessUnits, ...businessUnitData];
	// await companyDetails.save();
	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(
				StatusCodes.OK,
				'Business Units added successfully',
				companyDetails.businessUnits
			)
		);
};

const getAllBusinessUnits = async (req, res) => {
	// const { clientAdminId } = req.user;
	const company = await CompanyDetails.findOne({
		_id: req.user.companyId._id,
		// owner: clientAdminId,
		// clientAdmin: clientAdminId,
		// deleted: false,
	});
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const page = parseInt(req.query.page) || 1;
	let limit = parseInt(req.query.limit) || 10;
	if (req.query.limit === 'all') {
		limit = await BusinessUnit.countDocuments({ companyId: company._id });
	}
	const options = { page, limit };
	const businessUnitAggregateQuery = BusinessUnit.aggregate(
		getBusinessUnitsPipeline({ companyId: company._id, owner: company.owner })
	);
	const result = await BusinessUnit.aggregatePaginate(
		businessUnitAggregateQuery,
		options
	);

	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'Success', {
			businessUnits: result.docs,
			totalPages: result.totalPages,
			currentPage: result.page,
			totalResults: result.totalDocs,
		})
	);
};

const getPopulatedBusinessUnits = async (req, res) => {
	const company = await CompanyDetails.findOne({
		_id: req.user.companyId._id,
		// deleted: false,
	});

	if (!company) {
		throw new NotFoundError('Company Details not found');
	}
	console.log(` getPopulatedBusinessUnits - company:`, company._id);
	const businessUnits = await BusinessUnit.aggregate(
		getPopulatedBusinessUnitsPipeline(req.user.companyId._id)
	);
	console.log(` getPopulatedBusinessUnits - businessUnits:`, businessUnits);

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Success', businessUnits));
};

const updateBusinessUnit = async (req, res) => {
	const { _id, admin } = req.body;

	const branch = await BusinessUnit.findOne({ _id, deleted: false });

	if (!branch) {
		throw new NotFoundError('Branch not found');
	}

	if (admin) {
		const user = await User.findOne({
			_id: admin,
			deleted: false,
			companyId: branch.companyId,
			// businessUnitId: branch._id,
		});

		if (!user) {
			throw new NotFoundError('User not found to add as Business Unit Head', [
				'User should be an employee of the company',
				'User should be an employee of that business unit',
			]);
		}
	}

	await BusinessUnit.updateOne({ _id }, { $set: req.body });

	const updatedBranch = await BusinessUnit.findById(_id);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(
				StatusCodes.OK,
				'Business Unit updated successfully',
				updatedBranch
			)
		);
};

const deleteBusinessUnit = async (req, res) => {
	const { businessUnitIds } = req.body;

	const businessUnits = await BusinessUnit.find({
		_id: { $in: businessUnitIds },
		deleted: false,
	});
	if (businessUnits.length === 0) {
		throw new NotFoundError('Business units not found');
	}

	await BusinessUnit.updateMany(
		{ _id: { $in: businessUnitIds } },
		{ $set: { deleted: true } }
	);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, 'Business Units deleted successfully')
		);
};

const getBusinessUnitDepartments = async (req, res) => {
	const { id } = req.params;

	const businessUnit = await BusinessUnit.findById(id).select('name');
	const departments = await Department.find({
		businessUnitId: id,
		deleted: false,
	});
	if (departments.length === 0) {
		throw new NotFoundError(
			`Departments for business unit "${businessUnit.name}" not found`
		);
	}

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Success', departments));
};

module.exports = {
	getAllBusinessUnits,
	addBusinessUnit,
	updateBusinessUnit,
	getPopulatedBusinessUnits,
	deleteBusinessUnit,
	getBusinessUnitDepartments,
};
