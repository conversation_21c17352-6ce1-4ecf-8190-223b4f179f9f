const { StatusCodes } = require('http-status-codes');
const { Shift, User, EmploymentDetails } = require('../../models');
const { APIResponse, convertToObjectId } = require('../../utils');
const { NotFoundError } = require('../../errors');

const createShift = async (req, res) => {
	const { companyId } = req.user;
	const shift = await Shift.create({ ...req.body, companyId });
	res
		.status(StatusCodes.CREATED)
		.json(
			new APIResponse(StatusCodes.CREATED, 'Shift created successfully', shift)
		);
};

const getAllShifts = async (req, res) => {
	const page = parseInt(req.query.page) || 1;
	const limit = parseInt(req.query.limit) || 10;
	const { companyId } = req.user;
	const shiftsAggregateQuery = Shift.aggregate([
		{
			$match: {
				companyId: convertToObjectId(companyId._id),
			},
		},
	]);
	const options = { page, limit };
	const result = await Shift.aggregatePaginate(shiftsAggregateQuery, options);
	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'Shifts fetched successfully', {
			shifts: result.docs,
			totalPages: result.totalPages,
			currentPage: result.page,
			totalResults: result.totalDocs,
		})
	);
};

const assignShifts = async (req, res) => {
	const { shiftId, employeeIds } = req.body;

	const shift = await Shift.findById(shiftId);
	if (!shift) {
		throw new NotFoundError('Shift not found');
	}

	const employees = await EmploymentDetails.findOneAndUpdate(
		{
			userId: { $in: employeeIds },
		},
		{
			$set: { shiftId },
		}
	);

	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'Shifts assigned successfully', {
			shift,
			employees,
		})
	);
};

const getEmployeesForShiftAssignment = async (req, res) => {
	const { businessUnitIds, departmentIds, designationIds } = req.query;
	console.log(
		` getEmployeesForShiftAssignment - businessUnitIds:`,
		businessUnitIds
	);
	console.log(
		` getEmployeesForShiftAssignment - designationIds:`,
		designationIds
	);
	console.log(
		` getEmployeesForShiftAssignment - departmentIds:`,
		departmentIds
	);
	const matchConditions = [
		{ deleted: false }, // always required
	];

	// Business Units
	if (Array.isArray(businessUnitIds) && businessUnitIds.length > 0) {
		matchConditions.push({
			'employmentDetails.businessUnit': {
				$in: businessUnitIds.map((id) => convertToObjectId(id)),
			},
		});
	}

	// Departments
	if (Array.isArray(departmentIds) && departmentIds.length > 0) {
		matchConditions.push({
			'employmentDetails.department': {
				$in: departmentIds.map((id) => convertToObjectId(id)),
			},
		});
	}

	// Designations
	if (Array.isArray(designationIds) && designationIds.length > 0) {
		matchConditions.push({
			'employmentDetails.designation': {
				$in: designationIds.map((id) => convertToObjectId(id)),
			},
		});
	}

	// Shift conditions
	matchConditions.push({
		$or: [
			{ 'shift.default': true },
			{ 'employmentDetails.shiftId': null },
			{ 'employmentDetails.shiftId': { $exists: false } },
		],
	});

	const employees = await User.aggregate([
		{
			$match: {
				companyId: convertToObjectId(req.user.companyId._id),
				deleted: false,
			},
		},
		{
			$lookup: {
				from: 'employmentdetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'employmentDetails',
			},
		},
		{
			$addFields: {
				employmentDetails: { $first: '$employmentDetails' },
			},
		},
		{
			$lookup: {
				from: 'shifts',
				localField: 'employmentDetails.shiftId',
				foreignField: '_id',
				as: 'shift',
			},
		},
		{
			$addFields: {
				shift: { $first: '$shift' },
			},
		},
		{
			$match: {
				$and: matchConditions,
			},
		},
	]);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(
				StatusCodes.OK,
				'Employees fetched successfully',
				employees
			)
		);
};

const getSingleShiftDetails = async (req, res) => {
	const { shiftId } = req.params;

	const shift = await Shift.aggregate([
		{
			$match: {
				_id: convertToObjectId(shiftId),
				companyId: convertToObjectId(req.user.companyId._id),
				deleted: false,
			},
		},
		{
			$lookup: {
				from: 'employmentdetails',
				localField: '_id',
				foreignField: 'shiftId',
				as: 'employmentDetails',
			},
		},
		{
			$unwind: {
				path: '$employmentDetails',
				preserveNullAndEmptyArrays: true,
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				localField: 'employmentDetails.userId',
				foreignField: 'userId',
				as: 'personalDetails',
			},
		},
		{
			$lookup: {
				from: 'departments',
				localField: 'employmentDetails.department',
				foreignField: '_id',
				as: 'department',
			},
		},
		{
			$lookup: {
				from: 'designations',
				localField: 'employmentDetails.designation',
				foreignField: '_id',
				as: 'designation',
			},
		},
		{
			$lookup: {
				from: 'businessunits',
				localField: 'employmentDetails.businessUnit',
				foreignField: '_id',
				as: 'businessUnit',
			},
		},
		{
			$set: {
				personalDetails: {
					$first: '$personalDetails',
				},
				department: { $first: '$department' },
				designation: { $first: '$designation' },
				businessUnit: { $first: '$businessUnit' },
			},
		},
		{
			$group: {
				_id: '$_id', // shift ID
				shift: { $first: '$$ROOT' },
				employees: {
					$push: {
						$cond: [
							{ $gt: ['$employmentDetails', null] },
							{
								name: '$personalDetails.nameOnNRIC',
								department: '$department.name',
								designation: '$designation.name',
								businessUnit: '$businessUnit.name',
							},
							'$$REMOVE',
						],
					},
				},
			},
		},
		{
			$project: {
				_id: '$shift._id',
				name: '$shift.name',
				code: '$shift.code',
				type: '$shift.type',
				isDefault: '$shift.isDefault',
				companyId: '$shift.companyId',
				startTime: '$shift.startTime',
				endTime: '$shift.endTime',
				clockInLimit: '$shift.clockInLimit',
				clockOutLimit: '$shift.clockOutLimit',
				breakLimit: '$shift.breakLimit',
				clockInDelay: '$shift.clockInDelay',
				clockOutDelay: '$shift.clockOutDelay',
				tardinessMinutes: '$shift.tardinessMinutes',
				tardinessMinutesPerMonth: '$shift.tardinessMinutesPerMonth',
				workLocationCheckEnabled: '$shift.workLocationCheckEnabled',
				weekStartsFrom: '$shift.weekStartsFrom',
				allowedAttendanceDays: '$shift.allowedAttendanceDays',
				wifiCheckEnabled: '$shift.wifiCheckEnabled',
				OfficeWifiIPAddress: '$shift.OfficeWifiIPAddress',
				qrClockInCheckEnabled: '$shift.qrClockInCheckEnabled',
				facialCheckEnabled: '$shift.facialCheckEnabled',
				deleted: '$shift.deleted',
				workLocation: '$shift.workLocation',
				createdAt: '$shift.createdAt',
				updatedAt: '$shift.updatedAt',
				employees: 1,
			},
		},
	]);
	if (!shift[0]) {
		throw new NotFoundError('Shift not found');
	}
	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(
				StatusCodes.OK,
				'Shift details fetched successfully',
				shift
			)
		);
};

const updateShift = async (req, res) => {
	const { shiftId } = req.body;

	const updatedShift = await Shift.findOneAndUpdate(
		{ _id: shiftId },
		{ $set: { ...req.body } },
		{ new: true }
	);

	if (!updatedShift) {
		throw new NotFoundError('Shift not found');
	}
	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(
				StatusCodes.OK,
				'Shift updated successfully',
				updatedShift
			)
		);
};

const getGenericShifts = async (req, res) => {
	const genericShifts = await Shift.find({
		type: 'generic',
		companyId: req.user.companyId._id,
	});
	if (genericShifts.length === 0) {
		throw new NotFoundError('Generic Shifts not found');
	}
	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(
				StatusCodes.OK,
				'Shifts fetched successfully',
				genericShifts
			)
		);
};

const getRosterShifts = async (req, res) => {
	const rosterShifts = await Shift.find({
		type: 'roster',
		companyId: req.user.companyId._id,
	});
	if (rosterShifts.length === 0) {
		throw new NotFoundError('Roster Shifts not found');
	}
	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(
				StatusCodes.OK,
				'Shifts fetched successfully',
				rosterShifts
			)
		);
};

module.exports = {
	createShift,
	getAllShifts,
	assignShifts,
	getEmployeesForShiftAssignment,
	getSingleShiftDetails,
	updateShift,
	getGenericShifts,
	getRosterShifts,
};
