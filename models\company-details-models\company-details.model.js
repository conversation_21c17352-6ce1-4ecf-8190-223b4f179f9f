const mongoose = require('mongoose');
const autoPopulate = require('mongoose-autopopulate');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const mongooseDelete = require('mongoose-delete');

const CompanyDetailsSchema = new mongoose.Schema(
	{
		owner: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
		logo: {
			type: String,
			required: [true, 'Please provide logo'],
		},
		businessName: {
			type: String,
			required: [true, 'Please provide business name'],
			index: true,
		},
		clientAdmin: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
		businessCountry: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'Country',
			autopopulate: {
				select:
					'name iso2 iso3 currency currencyName currencySymbol phoneCode timezones',
			},
		},
		registration: {
			type: String,
			required: [true, 'Please provide registration'],
			index: true,
		},
		address: {
			type: String,
			required: [true, 'Please provide address'],
		},
		currency: {
			type: String,
		},
		timeFormat: {
			type: String,
		},
		dateFormat: {
			type: String,
		},
		monthlySchedule: {
			total: {
				type: Number,
				default: 12,
			},
			description: {
				type: String,
				default: 'Monthly rate multiplied by 12 months',
			},
		},
		dailySchedule: {
			total: {
				type: Number,
				default: 26,
			},
			description: {
				type: String,
				default: 'Monthly rate divided by 26 working days',
			},
		},
		hourlySchedule: {
			total: {
				type: Number,
				default: 8,
			},
			description: {
				type: String,
				default: 'Daily rate divided by 8 working hours',
			},
		},
	},
	{ timestamps: true }
);

CompanyDetailsSchema.index(
	{ businessName: 1, businessCountry: 1 },
	{ unique: true }
);
CompanyDetailsSchema.plugin(autoPopulate);
CompanyDetailsSchema.plugin(aggregatePaginate);
CompanyDetailsSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});

module.exports = mongoose.model('CompanyDetails', CompanyDetailsSchema);
