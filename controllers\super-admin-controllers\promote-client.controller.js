const { StatusCodes } = require('http-status-codes');
const { NotFoundError } = require('../../errors');
const { User } = require('../../models');
const { userRoles } = require('../../models/user.model');
const { APIResponse } = require('../../utils');

const promoteClientToGlorifiedClient = async (req, res) => {
	const { clientAdminId } = req.body;

	const clientAdmin = await User.findOne({
		_id: clientAdminId,
		deleted: false,
		role: userRoles.CLIENT_ADMIN,
		isOnboard: true,
	});

	if (!clientAdmin) {
		throw new NotFoundError('Client admin not found');
	}

	clientAdmin.role = userRoles.GLORIFIED_CLIENT_ADMIN;
	await clientAdmin.save();

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(
				StatusCodes.OK,
				'Client admin promoted to glorified client admin successfully',
				clientAdmin
			)
		);
};

const getAllClients = async (req, res) => {
	const page = req.query.page || 1;
	const limit = req.query.limit || 10;
	const options = { page, limit };

	const result = await User.aggregatePaginate(
		[
			{
				$match: {
					role: {
						$in: [userRoles.CLIENT_ADMIN, userRoles.GLORIFIED_CLIENT_ADMIN],
					},
					deleted: false,
					isOnboard: true,
				},
			},
			{
				$lookup: {
					from: 'personaldetails',
					localField: '_id',
					foreignField: 'userId',
					as: 'personalDetails',
				},
			},
			{
				$lookup: {
					from: 'companydetails',
					localField: 'companyId',
					foreignField: '_id',
					as: 'companyDetails',
				},
			},
			{
				$unwind: {
					path: '$companyDetails',
					preserveNullAndEmptyArrays: true,
				},
			},
			{
				$unwind: {
					path: '$personalDetails',
					preserveNullAndEmptyArrays: true,
				},
			},
			{
				$project: {
					_id: 1,
					name: '$personalDetails.nameOnNRIC',
					email: 1,
					role: 1,
					companyId: 1,
					companyName: '$companyDetails.businessName',
					isOwner: {
						$eq: ['$companyDetails.owner', '$companyDetails.clientAdmin'],
					},
					logo: '$companyDetails.logo',
				},
			},
		],
		options
	);

	if (result.docs.length === 0) {
		throw new NotFoundError('No clients found');
	}

	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'success', {
			clients: result.docs,
			totalPages: result.totalPages,
			currentPage: result.page,
			totalResults: result.totalDocs,
		})
	);
};

module.exports = {
	promoteClientToGlorifiedClient,
	getAllClients,
};
