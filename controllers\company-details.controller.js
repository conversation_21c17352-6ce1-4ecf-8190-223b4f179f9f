const { StatusCodes } = require('http-status-codes');
const { CompanyDetails, UserFlags } = require('../models');
const { APIResponse } = require('../utils');
const { NotFoundError } = require('../errors');

const getCompanyDetails = async (req, res) => {
	const { clientAdminId } = req.user;
	const ownerUserFlags = await UserFlags.findOne({ userId: clientAdminId });
	const companyDetails = await CompanyDetails.findOne({
		_id: req.user.companyId._id,
	});
	let company;
	if (ownerUserFlags.isRegistrationComplete === false) {
		company = { companyDetails, ownerUserFlags };
	} else {
		company = {
			companyDetails,
			ownerUserFlags: ownerUserFlags.isClientRegistrationAsEmployeeComplete,
		};
	}

	if (!company) {
		throw new NotFoundError('Company Details not found');
	}
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'success', company));
};

const updateWorkParameters = async (req, res) => {
	const company = await CompanyDetails.findOneAndUpdate(
		{ _id: req.user.companyId._id },
		{ $set: req.body }
	);
	res.status(StatusCodes.OK).json(new APIResponse(StatusCodes.OK, 'Success'));
};

module.exports = {
	getCompanyDetails,
	updateWorkParameters,
};
