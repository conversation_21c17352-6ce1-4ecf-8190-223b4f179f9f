const { convertToObjectId } = require('../../utils/misc');

const employeesListAggregation = ({ id, companyId }) => {
	return [
		{
			// Fetch only the employees associated with the respective client admin, excluding the client admin themselves
			$match: {
				_id: { $ne: convertToObjectId(id) },
				clientAdminId: convertToObjectId(id),
				companyId: convertToObjectId(companyId),
				deleted: false,
			},
		},
		{
			$lookup: {
				from: 'userflags',
				localField: '_id',
				foreignField: 'userId',
				as: 'userFlags',
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'personalDetails',
			},
		},
		{
			$lookup: {
				from: 'employmentdetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'employmentDetails',
			},
		},
		{
			$addFields: {
				personalDetails: {
					$first: '$personalDetails',
				},
				employmentDetails: {
					$first: '$employmentDetails',
				},
			},
		},
		{
			$lookup: {
				from: 'businessunits',
				localField: 'employmentDetails.businessUnit',
				foreignField: '_id',
				as: 'businessUnit',
			},
		},
		{
			$lookup: {
				from: 'departments',
				localField: 'employmentDetails.department',
				foreignField: '_id',
				as: 'department',
			},
		},
		{
			$lookup: {
				from: 'designations',
				localField: 'employmentDetails.designation',
				foreignField: '_id',
				as: 'designation',
			},
		},
		{
			$lookup: {
				from: 'users',
				localField: 'employmentDetails.reportingTo',
				foreignField: '_id',
				as: 'reportingTo',
			},
		},
		{
			$addFields: {
				reportingTo: {
					$first: '$reportingTo',
				},
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'reportingToPersonalDetails',
			},
		},
		{
			$addFields: {
				businessUnit: {
					$first: '$businessUnit',
				},
				department: {
					$first: '$department',
				},
				designation: {
					$first: '$designation',
				},
				'reportingTo.personalDetails': {
					$first: '$reportingToPersonalDetails',
				},
			},
		},
		{
			$project: {
				email: 1,
				isRegistrationComplete: 1,
				'personalDetails.employeeOrgId': 1,
				'personalDetails.nameOnNRIC': 1,
				'businessUnit.name': 1,
				'businessUnit.location': 1,
				'department.name': 1,
				'designation.name': 1,
				reportingTo: '$reportingTo.personalDetails.nameOnNRIC',
				'personalDetails.dateOfJoining': 1,
				userFlags: { $arrayElemAt: ['$userFlags', 0] },
			},
		},
	];
};
const employeesListForOrganizationAggregation = (companyId) => {
	return [
		{
			$match: {
				companyId: convertToObjectId(companyId),
				deleted: false,
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'personalDetails',
			},
		},
		{
			$lookup: {
				from: 'employmentdetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'employmentDetails',
			},
		},
		{
			$addFields: {
				personalDetails: {
					$first: '$personalDetails',
				},
				employmentDetails: {
					$first: '$employmentDetails',
				},
			},
		},
		{
			$lookup: {
				from: 'businessunits',
				localField: 'employmentDetails.businessUnit',
				foreignField: '_id',
				as: 'businessUnit',
			},
		},
		{
			$lookup: {
				from: 'departments',
				localField: 'employmentDetails.department',
				foreignField: '_id',
				as: 'department',
			},
		},
		{
			$lookup: {
				from: 'designations',
				localField: 'employmentDetails.designation',
				foreignField: '_id',
				as: 'designation',
			},
		},
		{
			$lookup: {
				from: 'users',
				localField: 'employmentDetails.reportingTo',
				foreignField: '_id',
				as: 'reportingTo',
			},
		},
		{
			$addFields: {
				reportingTo: {
					$first: '$reportingTo',
				},
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'reportingToPersonalDetails',
			},
		},
		{
			$addFields: {
				businessUnit: {
					$first: '$businessUnit',
				},
				department: {
					$first: '$department',
				},
				designation: {
					$first: '$designation',
				},
				'reportingTo.personalDetails': {
					$first: '$reportingToPersonalDetails',
				},
			},
		},
		{
			$project: {
				email: 1,
				isRegistrationComplete: 1,
				'personalDetails.employeeOrgId': 1,
				'personalDetails.nameOnNRIC': 1,
				'businessUnit.name': 1,
				'businessUnit.location': 1,
				'department.name': 1,
				'designation.name': 1,
				reportingTo: '$reportingTo.personalDetails.nameOnNRIC',
				'personalDetails.dateOfJoining': 1,
			},
		},
	];
};

const getSingleEmployeeAggregation = ({ clientAdminId, userId }) => {
	return [
		{
			$match: {
				clientAdminId: convertToObjectId(clientAdminId),
				_id: convertToObjectId(userId),
				deleted: false,
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'personalDetails',
			},
		},
		{
			$lookup: {
				from: 'familydetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'familyDetails',
			},
		},
		{
			$lookup: {
				from: 'educationdetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'education',
			},
		},
		{
			$lookup: {
				from: 'experiencedetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'experience',
			},
		},
		{
			$lookup: {
				from: 'skills',
				localField: '_id',
				foreignField: 'userId',
				as: 'skills',
			},
		},
		{
			$lookup: {
				from: 'earnings',
				localField: '_id',
				foreignField: 'userId',
				as: 'earnings',
			},
		},
		{
			$lookup: {
				from: 'benefits',
				localField: '_id',
				foreignField: 'userId',
				as: 'benefitDetails',
			},
		},
		{
			$lookup: {
				from: 'employmentdetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'employmentDetails',
			},
		},
		{
			$lookup: {
				from: 'contactdetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'contactDetails',
			},
		},
		{
			$lookup: {
				from: 'userflags',
				localField: '_id',
				foreignField: 'userId',
				as: 'userFlags',
			},
		},
		{
			$addFields: {
				personalDetails: {
					$first: '$personalDetails',
				},
				familyDetails: { $first: '$familyDetails' },
				skills: { $first: '$skills' },
				earnings: { $first: '$earnings' },
				benefitDetails: { $first: '$benefitDetails' },
				employmentDetails: {
					$first: '$employmentDetails',
				},
				userFlags: { $first: '$userFlags' },
			},
		},
		{
			$lookup: {
				from: 'businessunits',
				localField: 'employmentDetails.businessUnit',
				foreignField: '_id',
				as: 'employmentDetails.businessUnit',
			},
		},
		{
			$lookup: {
				from: 'departments',
				localField: 'employmentDetails.department',
				foreignField: '_id',
				as: 'employmentDetails.department',
			},
		},
		{
			$lookup: {
				from: 'designations',
				localField: 'employmentDetails.designation',
				foreignField: '_id',
				as: 'employmentDetails.designation',
			},
		},
		{
			$lookup: {
				from: 'users',
				localField: 'employmentDetails.reportingTo',
				foreignField: '_id',
				as: 'employmentDetails.reportingToUser',
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				localField: 'employmentDetails.reportingToUser._id',
				foreignField: 'userId',
				as: 'employmentDetails.reportingToPersonal',
			},
		},
		{
			$lookup: {
				from: 'holidaygroups',
				let: { groupIds: '$benefitDetails.holidayGroups' },
				pipeline: [
					{
						$match: {
							$expr: { $in: ['$_id', '$$groupIds'] },
						},
					},
					{
						$lookup: {
							from: 'holidays',
							localField: '_id',
							foreignField: 'holidayGroupId',
							as: 'holidays',
						},
					},
				],
				as: 'benefitDetails.holidayGroups',
			},
		},
		{
			$lookup: {
				from: 'leavegroups',
				let: { groupIds: '$benefitDetails.leaveGroups' },
				pipeline: [
					{
						$match: {
							$expr: { $in: ['$_id', '$$groupIds'] },
						},
					},
					{
						$lookup: {
							from: 'leaves',
							localField: '_id',
							foreignField: 'leaveGroupId',
							as: 'leaves',
						},
					},
				],
				as: 'benefitDetails.leaveGroups',
			},
		},
		{
			$addFields: {
				'employmentDetails.businessUnit': {
					$first: '$employmentDetails.businessUnit',
				},
				'employmentDetails.department': {
					$first: '$employmentDetails.department',
				},
				'employmentDetails.designation': {
					$first: '$employmentDetails.designation',
				},
				employmentDetails: {
					reportingTo: {
						$mergeObjects: [
							{ $arrayElemAt: ['$employmentDetails.reportingToUser', 0] },
							{ $arrayElemAt: ['$employmentDetails.reportingToPersonal', 0] },
						],
					},
				},
				// 'employmentDetails.reportingTo': {
				// 	$first: {
				// 		reportingToUser: '$employmentDetails.reportingToUser',
				// 		reportingToPersonal: '$employmentDetails.reportingToPersonal',
				// 	},
				// },
			},
		},
		{
			$project: {
				password: 0, // exclude sensitive fields if needed
			},
		},
	];
};

const getEmployeeHolidayGroups = (businessUnit, department, designation) => {
	return [
		{
			$facet: {
				designationHolidayGroups: [
					{
						$match: {
							'assignment.designation': convertToObjectId(designation),
							'assignment.employee': { $size: 0 },
						},
					},
					{ $project: { _id: 1 } },
				],
				departmentHolidayGroups: [
					{
						$match: {
							'assignment.department': convertToObjectId(department),
							'assignment.designation': {
								$size: 0,
							},
							'assignment.employee': { $size: 0 },
						},
					},
					{ $project: { _id: 1 } },
				],
				businessUnitHolidayGroups: [
					{
						$match: {
							'assignment.businessUnit': convertToObjectId(businessUnit),
							'assignment.department': { $size: 0 },
							'assignment.designation': {
								$size: 0,
							},
							'assignment.employee': { $size: 0 },
						},
					},
					{ $project: { _id: 1 } },
				],
			},
		},
		{
			$project: {
				holidayGroupIds: {
					$setUnion: [
						'$designationHolidayGroups',
						'$departmentHolidayGroups',
						'$businessUnitHolidayGroups',
					],
				},
			},
		},
		{
			$project: {
				_id: 0,
				holidayGroupIds: {
					$map: {
						input: '$holidayGroupIds',
						as: 'item',
						in: '$$item._id',
					},
				},
			},
		},
	];
};

const getEmployeesByHolidayGroupIdAggregation = (holidayGroupId) => {
	return [
		{
			$match: {
				holidayGroups: convertToObjectId(holidayGroupId),
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				localField: 'userId',
				foreignField: 'userId',
				as: 'personalDetails',
			},
		},
		{
			$lookup: {
				from: 'employmentdetails',
				localField: 'userId',
				foreignField: 'userId',
				as: 'employmentDetails',
			},
		},
		{
			$addFields: {
				employeeName: { $first: '$personalDetails.nameOnNRIC' },
				employmentDetails: { $first: '$employmentDetails' },
			},
		},
		{
			$lookup: {
				from: 'businessunits',
				localField: 'employmentDetails.businessUnit',
				foreignField: '_id',
				as: 'businessUnit',
			},
		},
		{
			$lookup: {
				from: 'departments',
				localField: 'employmentDetails.department',
				foreignField: '_id',
				as: 'department',
			},
		},
		{
			$lookup: {
				from: 'designations',
				localField: 'employmentDetails.designation',
				foreignField: '_id',
				as: 'designation',
			},
		},
		{
			$addFields: {
				businessUnitName: { $first: '$businessUnit.name' },
				businessUnitLocation: { $first: '$businessUnit.location' },
				departmentName: { $first: '$department.name' },
				designationName: { $first: '$designation.name' },
			},
		},
	];
};

const getEmployeeProfileDetailsAggregation = (id) => {
	return [
		// Match the specific user
		{
			$match: {
				_id: convertToObjectId(id),
			},
		},
		// Lookup personal details
		{
			$lookup: {
				from: 'personaldetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'personalDetails',
			},
		},
		{
			$addFields: {
				personalDetails: {
					$first: '$personalDetails',
				},
			},
		},
		// Lookup education details
		{
			$lookup: {
				from: 'educationdetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'education',
			},
		},
		// Lookup family details
		{
			$lookup: {
				from: 'familydetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'family',
			},
		},
		{
			$addFields: {
				family: { $first: '$family' },
			},
		},
		// Lookup experience details
		{
			$lookup: {
				from: 'experiencedetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'experience',
			},
		},
		// Lookup contact details
		{
			$lookup: {
				from: 'contactdetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'contact',
			},
		},
		// Lookup employment details
		{
			$lookup: {
				from: 'employmentdetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'employment',
			},
		},
		{
			$addFields: {
				employment: { $first: '$employment' },
			},
		},
		// Lookup designation details
		{
			$lookup: {
				from: 'designations',
				localField: 'employment.designation',
				foreignField: '_id',
				as: 'designation',
			},
		},
		{
			$addFields: {
				designation: { $first: '$designation' },
			},
		},
		// Lookup department details
		{
			$lookup: {
				from: 'departments',
				localField: 'employment.department',
				foreignField: '_id',
				as: 'department',
			},
		},
		{
			$addFields: {
				department: { $first: '$department' },
			},
		},
		// Lookup business unit details
		{
			$lookup: {
				from: 'businessunits',
				localField: 'employment.businessUnit',
				foreignField: '_id',
				as: 'businessUnit',
			},
		},
		{
			$addFields: {
				businessUnit: { $first: '$businessUnit' },
			},
		},
		// Lookup reporting manager's personal details
		{
			$lookup: {
				from: 'personaldetails',
				localField: 'employment.reportingTo',
				foreignField: 'userId',
				as: 'reportingToPersonal',
			},
		},
		{
			$addFields: {
				reportingToPersonal: {
					$first: '$reportingToPersonal',
				},
			},
		},
		// Lookup reporting manager's employment details
		{
			$lookup: {
				from: 'employmentdetails',
				localField: 'employment.reportingTo',
				foreignField: 'userId',
				as: 'reportingToEmployment',
			},
		},
		{
			$addFields: {
				reportingToEmployment: {
					$first: '$reportingToEmployment',
				},
			},
		},
		// Lookup reporting manager's designation
		{
			$lookup: {
				from: 'designations',
				localField: 'reportingToEmployment.designation',
				foreignField: '_id',
				as: 'reportingToDesignation',
			},
		},
		{
			$addFields: {
				reportingToDesignation: {
					$first: '$reportingToDesignation',
				},
			},
		},
		// Lookup reporting manager's department
		{
			$lookup: {
				from: 'departments',
				localField: 'reportingToEmployment.department',
				foreignField: '_id',
				as: 'reportingToDepartment',
			},
		},
		{
			$addFields: {
				reportingToDepartment: {
					$first: '$reportingToDepartment',
				},
			},
		},
		// Lookup reporting manager's business unit
		{
			$lookup: {
				from: 'businessunits',
				localField: 'reportingToEmployment.businessUnit',
				foreignField: '_id',
				as: 'reportingToBusinessUnit',
			},
		},
		{
			$addFields: {
				reportingToBusinessUnit: {
					$first: '$reportingToBusinessUnit',
				},
			},
		},
		// Construct the reportingTo object
		{
			$addFields: {
				reportingTo: {
					name: '$reportingToPersonal.nameOnNRIC',
					profilePhoto: '$reportingToPersonal.profilePhoto',
					designation: '$reportingToDesignation.name',
					department: '$reportingToDepartment.name',
					businessUnit: '$reportingToBusinessUnit.name',
				},
			},
		},
		// Final projection
		{
			$project: {
				personalDetails: 1,
				education: 1,
				family: 1,
				experience: 1,
				contact: 1,
				designation: '$designation.name',
				department: '$department.name',
				businessUnit: '$businessUnit.name',
				reportingTo: 1,
				email: 1,
				role: 1,
			},
		},
	];
};

const getModuleAdminsAggregation = ({ companyId, module }) => {
	return [
		{
			$match: {
				companyId: convertToObjectId(companyId),
				deleted: false,
				role: 7,
				moduleAdminAccess: module,
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'personalDetails',
			},
		},
		{
			$lookup: {
				from: 'employmentdetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'employmentDetails',
			},
		},
		{
			$addFields: {
				personalDetails: {
					$first: '$personalDetails',
				},
				employmentDetails: {
					$first: '$employmentDetails',
				},
			},
		},
		{
			$lookup: {
				from: 'businessunits',
				localField: 'employmentDetails.businessUnit',
				foreignField: '_id',
				as: 'businessUnit',
			},
		},
		{
			$lookup: {
				from: 'departments',
				localField: 'employmentDetails.department',
				foreignField: '_id',
				as: 'department',
			},
		},
		{
			$lookup: {
				from: 'designations',
				localField: 'employmentDetails.designation',
				foreignField: '_id',
				as: 'designation',
			},
		},
		{
			$lookup: {
				from: 'users',
				localField: 'employmentDetails.reportingTo',
				foreignField: '_id',
				as: 'reportingTo',
			},
		},
		{
			$addFields: {
				reportingTo: {
					$first: '$reportingTo',
				},
			},
		},
		{
			$lookup: {
				from: 'personaldetails',
				localField: '_id',
				foreignField: 'userId',
				as: 'reportingToPersonalDetails',
			},
		},
		{
			$addFields: {
				businessUnit: {
					$first: '$businessUnit',
				},
				department: {
					$first: '$department',
				},
				designation: {
					$first: '$designation',
				},
				'reportingTo.personalDetails': {
					$first: '$reportingToPersonalDetails',
				},
			},
		},
		{
			$project: {
				email: 1,
				role: 1,
				moduleAdminAccess: 1,
				'personalDetails.employeeOrgId': 1,
				'personalDetails.nameOnNRIC': 1,
				'businessUnit.name': 1,
				'businessUnit.location': 1,
				'department.name': 1,
				'designation.name': 1,
				reportingTo: '$reportingTo.personalDetails.nameOnNRIC',
				'personalDetails.dateOfJoining': 1,
			},
		},
	];
};

module.exports = {
	employeesListAggregation,
	getSingleEmployeeAggregation,
	getEmployeeHolidayGroups,
	getEmployeesByHolidayGroupIdAggregation,
	employeesListForOrganizationAggregation,
	getEmployeeProfileDetailsAggregation,
	getModuleAdminsAggregation,
};
