const onboardingLinkEmailTemplate = ({
	name,
	email,
	token,
	origin,
	companyName,
}) => {
	return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Complete Your Registration</title>
  <style>
    body {
      font-family: 'Segoe UI', sans-serif;
      background-color: #f4f4f4;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 30px auto;
      background-color: #ffffff;
      border-radius: 10px;
      padding: 30px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    h2 {
      color: #333333;
    }
    p {
      color: #555555;
      line-height: 1.5;
    }
    .btn {
      display: inline-block;
      margin-top: 20px;
      padding: 12px 25px;
      background-color: #007bff;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      font-weight: bold;
    }
    .btn:hover {
      background-color: #0056b3;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>Welcome Aboard!</h2>
    <p>Hello ${name},</p>
    <p>You’ve been invited to join ${companyName}. Please click the button below to complete your registration by filling out your details.</p>
    <a href="${origin}/onboarding-form/${token}" class="btn">Complete Registration</a>
    <p>If you weren’t expecting this invitation, you can safely ignore this email.</p>
    <p>Cheers,<br/>The ${companyName} Team</p>
  </div>
</body>
</html>
`;
};

module.exports = { onboardingLinkEmailTemplate };
