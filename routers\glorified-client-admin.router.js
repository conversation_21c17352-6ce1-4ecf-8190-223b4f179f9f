const express = require('express');
const router = express.Router();

// Middlewares
const {
	authenticationMiddleware,
	authorizePermissions,
	createCompanyMiddleware,
	uploadImage,
} = require('../middlewares');

const { userRoles } = require('../models/user.model');

// Controllers
const {
	getAllCompanies,
	addClientAdmin,
	removeClientAdmin,
	changeCurrentCompany,
	createCompany,
	getAllEmployeesOfOrganization,
} = require('../controllers/glorified-client-admin.controller');

/**
 * @route   GET /
 * @desc    Get all companies visible to the glorified client admin
 * @access  Private (GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/companies')
	.get(
		authenticationMiddleware,
		authorizePermissions(userRoles.GLORIFIED_CLIENT_ADMIN),
		getAllCompanies
	);

/**
 * @route   POST /add-company
 * @desc    Create a new company (with logo upload)
 * @access  Private (GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/add-company')
	.post(
		authenticationMiddleware,
		authorizePermissions(userRoles.GLORIFIED_CLIENT_ADMIN),
		uploadImage.single('logo'),
		createCompanyMiddleware,
		createCompany
	);

/**
 * @route   GET /change-company/:companyId
 * @desc    Switch current working company context
 * @access  Private (GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/change-company/:companyId')
	.get(
		authenticationMiddleware,
		authorizePermissions(userRoles.GLORIFIED_CLIENT_ADMIN),
		changeCurrentCompany
	);

/**
 * @route   PATCH /assign-admin
 * @desc    Assign a Client Admin to a company
 * @access  Private (GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/assign-admin')
	.patch(
		authenticationMiddleware,
		authorizePermissions(userRoles.GLORIFIED_CLIENT_ADMIN),
		addClientAdmin
	);

/**
 * @route   PATCH /unassign-admin
 * @desc    Remove (unassign) a Client Admin from a company
 * @access  Private (GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/unassign-admin')
	.patch(
		authenticationMiddleware,
		authorizePermissions(userRoles.GLORIFIED_CLIENT_ADMIN),
		removeClientAdmin
	);

/**
 * @route   GET /employees/:companyId
 * @desc    Get all employees of a specific company
 * @access  Private (GLORIFIED_CLIENT_ADMIN)
 */
router
	.route('/employees/:companyId')
	.get(
		authenticationMiddleware,
		authorizePermissions(userRoles.GLORIFIED_CLIENT_ADMIN),
		getAllEmployeesOfOrganization
	);

// TODO: create a controller for fetching companies based on their location
// router
// 	.route('/:countryId')
// 	.get(
// 		authenticationMiddleware,
// 		authorizePermissions(userRoles.GLORIFIED_CLIENT_ADMIN),
// 		getAllCompanies
// 	);

module.exports = router;
