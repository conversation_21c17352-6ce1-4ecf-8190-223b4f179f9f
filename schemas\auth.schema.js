const { z } = require('zod');
const { convertToObjectId } = require('../utils');

const loginSchema = z
	.object({
		email: z.string().email('Invalid Email Format').min(5),
		password: z.string(),
	})
	.strict();

const mobileLoginSchema = z
	.object({
		countryId: z.string().refine((val) => convertToObjectId(val), {
			message: 'Invalid country id',
		}),
		mobile: z
			.string()
			.regex(/^[0-9]+$/, 'Mobile number must contain only numbers')
			.min(8)
			.max(15),
		password: z.string(),
	})
	.strict();

const registerEmailSchema = z
	.object({
		email: z.string().email('Invalid Email Format').min(5),
	})
	.strict();

const verifyEmailSchema = z
	.object({
		email: z.string().email('Invalid Email Format').min(5),
		verificationToken: z
			.string()
			.min(6, 'Invalid Verification Token')
			.max(6, 'Invalid Verification Token')
			.regex(/^\d{6}$/, 'Invalid Verification Token'),
	})
	.strict();

const registerSchema = z
	.object({
		email: z.string().email('Invalid Email Format').min(5),
		password: z
			.string()
			.regex(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{8,}$/gm, {
				message:
					'Password must contain at least 8 characters, one uppercase letter, one lowercase letter and one number',
			}),
	})
	.strict();

const forgotPasswordSchema = z
	.object({
		email: z.string().email('Invalid Email Format').min(5),
	})
	.strict();

const updatePasswordSchema = z
	.object({
		password: z
			.string()
			.regex(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{8,}$/gm, {
				message:
					'Password must contain at least 8 characters, one uppercase letter, one lowercase letter and one number',
			}),
		email: z.string().email('Invalid Email Format'),
		token: z.string().regex(/^[a-zA-Z0-9]{140}$/, {
			message: 'Invalid token',
		}),
	})
	.strict();

module.exports = {
	loginSchema,
	registerSchema,
	updatePasswordSchema,
	registerEmailSchema,
	verifyEmailSchema,
	forgotPasswordSchema,
	mobileLoginSchema,
};
