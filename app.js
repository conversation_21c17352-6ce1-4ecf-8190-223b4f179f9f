require('dotenv').config();
require('express-async-errors');

const http = require('http');
const { Server } = require('socket.io');
const express = require('express');
const app = express();
const crypto = require('crypto');

// Core dependencies
const morgan = require('morgan');
const cors = require('cors');
const helmet = require('helmet');
const xss = require('xss-clean');
const cookieParser = require('cookie-parser');

// Utilities and custom modules
const connectDB = require('./db/connect');
const { logger } = require('./utils');
const seedCountries = require('./scripts/seedCountries');
const { CustomError } = require('./errors');

// Middlewares
const {
	notFoundMiddleware,
	errorHandlingMiddleware,
	clientIpMiddleware,
	rateLimiter,
} = require('./middlewares');

// Routers
const routes = require('./routers');
const { initSocket } = require('./sockets');

// =====================
// Security Middlewares
// =====================

// Rate limiting to prevent abuse
app.set('trust proxy', 1);
app.use(rateLimiter);

// Set security HTTP headers
app.use(helmet());

// Prevent XSS attacks
app.use(xss());

// Parse cookies (signed with JWT secret)
app.use(cookieParser(process.env.JWT_SECRET));

// =====================
// Body Parsing
// =====================

app.use(
	express.json({
		limit: '16kb',
	})
);
app.use(
	express.urlencoded({
		limit: '16kb',
		extended: true,
	})
);

// =====================
// CORS Configuration
// =====================

app.use(
	cors({
		origin: (origin, callback) => {
			const allowedOrigins = [
				process.env.FRONTEND_ORIGIN,
				process.env.DEVELOPMENT_ORIGIN,
				process.env.STAGING_ORIGIN,
			];
			if (!origin || allowedOrigins.includes(origin)) {
				callback(null, true);
			} else {
				callback(new CustomError('Not allowed by CORS'));
			}
		},
		credentials: true,
	})
);

// =====================
// Logging
// =====================

app.use(
	morgan(':method :url :status :res[content-length] - :response-time ms', {
		stream: {
			write: (message) => logger.http(message.trim()),
		},
	})
);

// Socket Initialization
const server = http.createServer(app);
initSocket(server);

// =====================
// Static Files & Custom Middlewares
// =====================

app.use(express.static('./public'));
app.use(clientIpMiddleware);

// =====================
// API Routes
// =====================

app.use('/api/v1', routes);

// =====================
// Health Check / Root Redirect
// =====================

app.get('/', (req, res) => {
	res.redirect('https://tech.valluva.com/');
});

// =====================
// Error Handling
// =====================

app.use(notFoundMiddleware);
app.use(errorHandlingMiddleware);

// =====================
// Server & Database Startup
// =====================

if (process.env.NODE_ENV === 'production') {
	logger.info('Starting cron jobs');
	require('./crons/clockOut.cron'); // it will run everyday at 01:00
}

const port = process.env.PORT || 5000;

const start = async () => {
	try {
		// Choose DB URI and name based on environment
		const dbUri =
			process.env.NODE_ENV === 'production'
				? process.env.MONGO_URI_PRODUCTION
				: process.env.MONGO_URI_DEVELOPMENT;
		const dbName =
			process.env.NODE_ENV === 'production'
				? process.env.DB_NAME_PRODUCTION
				: process.env.DB_NAME_DEVELOPMENT;

		const connectionInstance = await connectDB(`${dbUri}/${dbName}`);
		console.log(
			`MongoDB Connected! DB Host: ${connectionInstance.connection.host}`
		);

		// Optionally seed countries (uncomment if needed)
		// await seedCountries();

		server.listen(port, () => {
			console.log(`Server is listening on port ${port}`);
		});
	} catch (error) {
		logger.error(error.message);
		console.error(`MongoDB connection error: ${error}`);
		process.exit(1);
	}
};

start();
