const mongoose = require('mongoose');

const TokenSchema = new mongoose.Schema({
	refreshToken: {
		type: String,
		required: true,
	},
	ip: {
		type: String,
		required: true,
	},
	userAgent: {
		type: String,
		required: true,
	},
	user: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'User',
	},
	isValid: {
		type: Boolean,
		default: true,
	},
});

module.exports = mongoose.model('Token', TokenSchema);
