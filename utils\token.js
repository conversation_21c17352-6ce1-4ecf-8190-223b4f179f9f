const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { logger } = require('./logger');
const dayjs = require('dayjs');
const { UnauthorizedError } = require('../errors');
const { Token } = require('../models');
const { APIResponse } = require('./APIResponse');

const createToken = ({ payload }) => {
	const token = jwt.sign(payload, process.env.JWT_SECRET);
	return token;
};

const verifyToken = (token) => jwt.verify(token, process.env.JWT_SECRET);

const createTokenUser = (user) => {
	return {
		email: user.email,
		role: user.role,
		userId: user.userId ? user.userId : user._id,
		clientAdminId: user.clientAdminId,
		companyId: user.companyId,
		isVerified: user.isVerified,
		isOnboard: user.isOnboard,
		isOnboardStepOneComplete: user.isOnboardStepOneComplete,
	};
};
const isMobileRequest = (req) => {
	const userAgent = req.headers['user-agent'] || '';
	const secMobile = req.headers['sec-ch-ua-mobile'];
	const clientType = req.headers['x-client-type'];
	// console.log(` isMobileRequest - userAgent:`, userAgent);
	// console.log(` isMobileRequest - userAgent:`, userAgent);
	// console.log(` isMobileRequest - secMobile:`, secMobile);
	// logger.info(
	// 	` isMobileRequest - userAgent: ${userAgent}, secMobile: ${secMobile}, clientType: ${clientType}`
	// );
	return (
		clientType === 'mobile' ||
		secMobile === '?1' ||
		/mobile|android|iphone|ipad|ipod/i.test(userAgent)
	);
};

const generateToken = ({ user, refreshToken }) => {
	const tokenUser = createTokenUser(user);
	const accessTokenJWT = encryptToken(createToken({ payload: tokenUser }));
	const refreshTokenJWT = encryptToken(
		createToken({ payload: { tokenUser, refreshToken } })
	);

	return { accessTokenJWT, refreshTokenJWT };
};

const attachCookiesToResponse = ({
	isMobile,
	res,
	user,
	refreshToken,
	rememberMe,
}) => {
	const { accessTokenJWT, refreshTokenJWT } = generateToken({
		user,
		refreshToken,
	});

	const fifteenMinutes = 1000 * 60 * 15;
	const oneDay = 1000 * 60 * 60 * 24;
	const oneMonth = 1000 * 60 * 60 * 24 * 30;

	if (isMobile) {
		res.setHeader('x-access-token', accessTokenJWT);
		res.setHeader('x-refresh-token', refreshTokenJWT);
		res.setHeader('x-token-refreshed', 'true');
	} else {
		res.cookie('accessToken', accessTokenJWT, {
			httpOnly: true,
			signed: true,
			secure: true,
			// secure: process.env.NODE_ENV === "production",
			sameSite: 'none',
			maxAge: dayjs().add(15, 'minute').toDate(),
		});
		res.cookie('refreshToken', refreshTokenJWT, {
			httpOnly: true,
			secure: true,
			// secure: process.env.NODE_ENV === "production",
			sameSite: 'none',
			signed: true,
			expires:
				rememberMe === 'on'
					? dayjs().add(30, 'day').toDate()
					: dayjs().add(1, 'day').toDate(),
		});
	}
};

const encryptToken = (token) => {
	try {
		const algorithm = process.env.ENCRYPTION_ALG;
		const secretKey = Buffer.from(process.env.ENCRYPTION_SECRET, 'hex');
		const iv = Buffer.from(process.env.ENCRYPTION_IV, 'hex');
		const cipher = crypto.createCipheriv(algorithm, secretKey, iv);
		let encryptedContent = cipher.update(token, 'utf-8', 'hex');
		encryptedContent += cipher.final('hex');
		return encryptedContent;
	} catch (error) {
		logger.error(error);
		throw new UnauthorizedError('Authentication Failed, Contact Admin', [
			'Token Encryption Failed',
		]);
	}
};

const decryptToken = (encryptedToken) => {
	try {
		const algorithm = process.env.ENCRYPTION_ALG;
		const secretKey = Buffer.from(process.env.ENCRYPTION_SECRET, 'hex');
		const iv = Buffer.from(process.env.ENCRYPTION_IV, 'hex');
		const decipher = crypto.createDecipheriv(algorithm, secretKey, iv);
		let decryptedContent = decipher.update(encryptedToken, 'hex', 'utf-8');
		decryptedContent += decipher.final('utf-8');
		return decryptedContent;
	} catch (error) {
		logger.error(error);
		throw new UnauthorizedError('Authentication Failed, Contact Admin', [
			'Token Decryption Failed',
		]);
	}
};

const getOrCreateRefreshToken = async ({ req, res, user }) => {
	let refreshToken = '';
	const existingToken = await Token.findOne({ user: user._id });
	if (existingToken) {
		const { isValid } = existingToken;
		if (!isValid) {
			throw new UnauthorizedError('Authentication Failed, Contact Admin');
		}
		refreshToken = existingToken.refreshToken;
		attachCookiesToResponse({ res, user, refreshToken });
	}

	refreshToken = crypto.randomBytes(40).toString('hex');
	const userAgent = req.headers['user-agent'];
	const ip = `${req.ip}, ${req.clientIp}`;
	await Token.create({ refreshToken, ip, userAgent, user: user._id });
	attachCookiesToResponse({ res, user, refreshToken });
};

module.exports = {
	attachCookiesToResponse,
	verifyToken,
	createTokenUser,
	createToken,
	decryptToken,
	encryptToken,
	isMobileRequest,
	generateToken,
	getOrCreateRefreshToken,
};
