const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

const { convertToObjectId } = require('../../utils/misc');
const { BadRequestError } = require('../../errors');

const DesignationSchema = new mongoose.Schema({
	name: {
		type: String,
		required: true,
	},
	businessUnitId: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'BusinessUnit',
		required: true,
	},
	departmentId: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'Department',
		required: true,
	},
	companyId: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'CompanyDetails',
		required: true,
	},
});

DesignationSchema.pre(
	'updateMany',
	{ document: false, query: true },
	async function (next) {
		const update = this.getUpdate();
		const filter = this.getFilter();
		if (update['$set']?.deleted === true) {
			const designationIds = filter._id.$in
				? filter._id.$in.map((id) => convertToObjectId(id))
				: [convertToObjectId(filter._id)];
			console.log(`line 46: designation.model designationIds:`, designationIds);

			const hasEmployees = await mongoose.model('EmploymentDetails').exists({
				designation: { $in: designationIds },
				deleted: false,
			});
			console.log(` hasEmployees:`, hasEmployees);

			if (hasEmployees) {
				return next(
					new BadRequestError(
						'Please transfer the employees of the designation to other designation first, then delete.'
					)
				);
			}
		}
		next();
	}
);
DesignationSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
DesignationSchema.plugin(autoPopulate);
DesignationSchema.plugin(aggregatePaginate);

module.exports = mongoose.model('Designation', DesignationSchema);
