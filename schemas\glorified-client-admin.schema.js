const { z } = require('zod');

const createCompanySchema = z
	.object({
		businessName: z
			.string()
			.min(2, 'Business name must be at least 2 characters'),
		businessCountry: z.string().min(2, 'Please select a country'),
		registration: z.string().min(2, 'Registration number is required'),
		address: z.string().min(5, 'Address must be at least 5 characters'),
		currency: z.string().min(1, 'Please select a currency'),
		timeFormat: z.enum(['12h', '24h'], {
			errorFormat: () => ({
				message: 'Please select a time format',
			}),
		}),
		dateFormat: z.enum(['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY/MM/DD'], {
			errorFormat: () => ({
				message: 'Please select a date format',
			}),
		}),
	})
	.strict();

module.exports = {
	createCompanySchema,
};
