const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

const TaskGroupSchema = new mongoose.Schema({
	name: {
		type: String,
		required: true,
	},
	projectId: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'Project',
		required: [true, 'Project is required'],
	},
	companyId: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'CompanyDetails',
		required: [true, 'Company is required'],
	},
	taskIds: [
		{
			type: mongoose.Schema.Types.ObjectId,
			ref: 'Task',
		},
	],
	color: {
		type: String,
	},
});

const TasksSchema = new mongoose.Schema(
	{
		name: {
			type: String,
			required: [true, 'Task name is required'],
			// unique: true,
			lowercase: true,
		},
		code: {
			type: String,
			required: [true, 'Task code is required'],
			unique: true,
		},
		description: {
			type: String,
			default: '',
		},
		status: {
			type: String,
			enum: ['pending', 'completed', 'in-progress', 'cancelled', 'overdue'],
			default: 'pending',
		},
		assignedTo: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
			// required: [true, 'Assigned to is required'],
		},
		assignedBy: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
			// required: [true, 'Assigned by is required'],
		},
		projectId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'Project',
			required: [true, 'Project is required'],
		},
		dueDate: {
			type: Date,
			required: [true, 'Due date is required'],
		},
		priority: {
			type: String,
			enum: ['low', 'medium', 'high'],
			default: 'low',
		},
		coverImage: {
			type: String,
		},
		media: [
			{
				url: String,
				public_id: String,
				resource_type: String, // 'image' or 'video'
			},
		],
		color: {
			type: String,
		},
		comments: [
			{
				userId: {
					type: mongoose.Schema.Types.ObjectId,
					ref: 'User',
				},
				comment: {
					type: String,
				},
				createdAt: {
					type: Date,
					default: Date.now,
				},
			},
		],
		icon: {
			type: String,
		},
	},
	{ timestamps: true }
);

TasksSchema.plugin(autoPopulate);
TasksSchema.plugin(aggregatePaginate);
TasksSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});

const Task = mongoose.model('Task', TasksSchema);
const TaskGroup = mongoose.model('TaskGroup', TaskGroupSchema);

module.exports = {
	Task,
	TaskGroup,
};
