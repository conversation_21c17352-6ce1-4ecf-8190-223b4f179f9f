const express = require('express');
const router = express.Router();
const {
	getEmployeesForProjects,
	createProject,
	switchProjectLead,
	deleteProject,
	getProjects,
	updateProject,
	getSingleProject,
	assignProjectToEmployees,
	removeEmployeesFromProject,
} = require('../controllers/projects-and-tasks-module-controllers/projects.controller');
const {
	authenticationMiddleware,
	authorizePermissions,
	createProjectMiddleware,
	switchProjectLeadMiddleware,
	updateProjectMiddleware,
	getSingleProjectMiddleware,
	deleteProjectMiddleware,
} = require('../middlewares');
const { userRoles } = require('../models/user.model');
const {
	assignProjectToEmployeesMiddleware,
} = require('../middlewares/projectsAndTasksMiddleware');

/**
 * @swagger
 * /api/v1/projects/employees:
 *   get:
 *     summary: Get employees for a project
 *     description: Get employees for a project
 *     tags:
 *       - Projects
 *     security:
 *       - Bearer: []
 *     responses:
 *       200:
 *         description: Employees data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 employees:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Employee'
 */
router
	.route('/employees')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.BUSINESS_ADMIN,
			userRoles.DEPARTMENT_ADMIN
		),
		getEmployeesForProjects
	);

/**
 * @swagger
 * /api/v1/projects/:
 *   post:
 *     summary: Create a new project
 *     description: Create a new project
 *     tags:
 *       - Projects
 *     security:
 *       - Bearer: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Project'
 *     responses:
 *       201:
 *         description: Project created
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 */
router
	.route('/')
	.post(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.BUSINESS_ADMIN,
			userRoles.DEPARTMENT_ADMIN
		),
		createProjectMiddleware,
		createProject
	);

/**
 * @swagger
 * /api/v1/projects/change-lead:
 *   patch:
 *     summary: Switch project lead
 *     description: Switch project lead
 *     tags:
 *       - Projects
 *     security:
 *       - Bearer: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Project'
 *     responses:
 *       200:
 *         description: Project lead switched
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 */
router
	.route('/change-lead')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.BUSINESS_ADMIN,
			userRoles.DEPARTMENT_ADMIN
		),
		switchProjectLeadMiddleware,
		switchProjectLead
	);

/**
 * @swagger
 * /api/v1/projects/{projectId}:
 *   delete:
 *     summary: Delete a project
 *     description: Delete a project
 *     tags:
 *       - Projects
 *     security:
 *       - Bearer: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         schema:
 *           type: integer
 *         required: true
 *         description: Project ID
 *     responses:
 *       200:
 *         description: Project deleted
 */
router
	.route('/:projectId')
	.delete(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.BUSINESS_ADMIN,
			userRoles.DEPARTMENT_ADMIN
		),
		deleteProjectMiddleware,
		deleteProject
	);

/**
 * @swagger
 * /api/v1/projects/:
 *   get:
 *     summary: Get all projects
 *     description: Get all projects
 *     tags:
 *       - Projects
 *     security:
 *       - Bearer: []
 *     responses:
 *       200:
 *         description: Projects data
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Project'
 */
router
	.route('/')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.BUSINESS_ADMIN,
			userRoles.DEPARTMENT_ADMIN
		),
		getProjects
	);

/**
 * @swagger
 * /api/v1/projects/{projectId}:
 *   patch:
 *     summary: Update a project
 *     description: Update a project
 *     tags:
 *       - Projects
 *     security:
 *       - Bearer: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         schema:
 *           type: integer
 *         required: true
 *         description: Project ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Project'
 *     responses:
 *       200:
 *         description: Project updated
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 */
router
	.route('/')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.BUSINESS_ADMIN,
			userRoles.DEPARTMENT_ADMIN
		),
		updateProjectMiddleware,
		updateProject
	);

/**
 * @swagger
 * /api/v1/projects/{projectId}:
 *   get:
 *     summary: Get a single project
 *     description: Get a single project
 *     tags:
 *       - Projects
 *     security:
 *       - Bearer: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         schema:
 *           type: integer
 *         required: true
 *         description: Project ID
 *     responses:
 *       200:
 *         description: Project data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 */
router
	.route('/:projectId')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.BUSINESS_ADMIN,
			userRoles.DEPARTMENT_ADMIN
		),
		getSingleProjectMiddleware,
		getSingleProject
	);

router
	.route('/assign-employees')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.BUSINESS_ADMIN,
			userRoles.DEPARTMENT_ADMIN
		),
		assignProjectToEmployeesMiddleware,
		assignProjectToEmployees
	);

router
	.route('/remove-employees')
	.patch(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN,
			userRoles.BUSINESS_ADMIN,
			userRoles.DEPARTMENT_ADMIN
		),
		assignProjectToEmployeesMiddleware,
		removeEmployeesFromProject
	);

module.exports = router;
