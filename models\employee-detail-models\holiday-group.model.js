const { default: mongoose } = require('mongoose');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const mongooseDelete = require('mongoose-delete');
const autoPopulate = require('mongoose-autopopulate');

const HolidayGroupSchema = new mongoose.Schema(
	{
		companyId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'CompanyDetails',
			required: [true, 'CompanyId is required in holiday'],
		},
		name: {
			type: String,
			lowercase: true,
			required: [true, 'Group name is required in holiday'],
		},
		assignment: {
			businessUnit: [
				{
					type: mongoose.Schema.Types.ObjectId,
					ref: 'BusinessUnit',
				},
			],
			department: [
				{
					type: mongoose.Schema.Types.ObjectId,
					ref: 'Department',
				},
			],
			designation: [
				{
					type: mongoose.Schema.Types.ObjectId,
					ref: 'Designation',
				},
			],
			employee: [
				{
					type: mongoose.Schema.Types.ObjectId,
					ref: 'User',
				},
			],
		},
	},
	{ timestamps: true }
);

HolidayGroupSchema.plugin(aggregatePaginate);
HolidayGroupSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
HolidayGroupSchema.plugin(autoPopulate);
module.exports = mongoose.model('HolidayGroup', HolidayGroupSchema);
