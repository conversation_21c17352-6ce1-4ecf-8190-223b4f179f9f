const { StatusCodes } = require('http-status-codes');
const {
	getHolidayGroupsPipeline,
} = require('../db/aggregations/company-details.aggregation');
const { NotFoundError, ConflictError, BadRequestError } = require('../errors');
const {
	CompanyDetails,
	HolidayGroup,
	Designation,
	Benefits,
	Holiday,
	EmploymentDetails,
} = require('../models');
const { APIResponse, convertToObjectId } = require('../utils');
const dayjs = require('dayjs');

const getHolidayGroups = async (req, res) => {
	const company = await CompanyDetails.findOne({ _id: req.user.companyId._id });
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const page = parseInt(req.query.page) || 1;
	const limit = parseInt(req.query.limit) || 10;
	const options = { page, limit };
	const holidayGroupAggregateQuery = HolidayGroup.aggregate(
		getHolidayGroupsPipeline(company._id)
	);
	const result = await HolidayGroup.aggregatePaginate(
		holidayGroupAggregateQuery,
		options
	);

	if (result.docs === 0) {
		throw new NotFoundError('HolidayGroups not found');
	}

	res.status(StatusCodes.OK);
	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'Success', {
			holidayGroups: result.docs,
			totalPages: result.totalPages,
			currentPage: result.page,
			totalResults: result.totalDocs,
		})
	);
};

const getHolidayGroupDetails = async (req, res) => {
	const { id } = req.params;

	const designations = await Designation.find({
		departmentId: id,
		deleted: false,
	});

	if (designations.length === 0) {
		throw new NotFoundError(`Designations for department id: ${id} not found`);
	}

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'Success', designations));
};

const addHolidayGroup = async (req, res) => {
	const { name, assignTo } = req.body;

	const company = await CompanyDetails.findOne({ _id: req.user.companyId._id });
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const existingHolidayGroups = await HolidayGroup.find({
		companyId: company._id,
		name,
		deleted: false,
	});
	if (existingHolidayGroups.length > 0) {
		throw new ConflictError(
			'Holiday Group with the same name already exists',
			existingHolidayGroups
		);
	}

	const userIds = await getEmployeeIdsByAssignment(assignTo);
	// if(userIds.length === 0) {
	//   throw new NotFoundError("Employees not found in selected assignment");
	// }

	const holidayGroup = await HolidayGroup.create({
		...req.body,
		companyId: company._id,
	});

	await Benefits.updateMany(
		{ userId: { $in: userIds } },
		{ $addToSet: { holidayGroups: holidayGroup._id } }
	);

	res
		.status(StatusCodes.CREATED)
		.json(
			new APIResponse(
				StatusCodes.CREATED,
				'Holiday groups added successfully',
				holidayGroup
			)
		);
};

const copyHolidayGroup = async (req, res) => {
	const { name, assignTo, holidays } = req.body;

	const company = await CompanyDetails.findOne({ _id: req.user.companyId._id });
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const existingHolidayGroups = await HolidayGroup.find({
		companyId: company._id,
		name,
		deleted: false,
	});
	if (existingHolidayGroups.length > 0) {
		throw new ConflictError(
			'Holiday Group with the same name already exists',
			existingHolidayGroups
		);
	}

	const userIds = await getEmployeeIdsByAssignment(assignTo);
	// if(userIds.length === 0) {
	//   throw new NotFoundError("Employees not found in selected assignment");
	// }

	const holidayGroup = await HolidayGroup.create({
		...req.body,
		companyId: company._id,
	});
	const createdHolidays = holidays.map((holiday) => {
		const { startDate, endDate } = holiday;
		let numberOfDays = 1;
		if (startDate && endDate) {
			numberOfDays = dayjs(endDate).diff(dayjs(startDate), 'day') + 1;
		}

		const formattedHoliday = {
			...holiday,
			numberOfDays,
			endDate: endDate ? endDate : null,
			holidayGroupId: holidayGroup._id,
			companyId: company._id,
		};

		return formattedHoliday;
	});
	await Holiday.insertMany(createdHolidays);

	await Benefits.updateMany(
		{ userId: { $in: userIds } },
		{ $addToSet: { holidayGroups: holidayGroup._id } }
	);

	res
		.status(StatusCodes.CREATED)
		.json(
			new APIResponse(
				StatusCodes.CREATED,
				'Holiday groups added successfully',
				holidayGroup
			)
		);
};

const updateHolidayGroup = async (req, res) => {
	const { _id, name, assignTo } = req.body;

	const company = await CompanyDetails.findOne({ _id: req.user.companyId._id });
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const existingHolidayGroups = await HolidayGroup.findOne({
		_id: { $ne: _id },
		name: name.toLowerCase(),
		companyId: company._id,
		deleted: false,
	});
	if (existingHolidayGroups) {
		throw new ConflictError(
			'Holiday Group with the same name already exists',
			existingHolidayGroups
		);
	}

	// remove holiday groups from exitsing employees
	await removeHolidayGroupFromEmployees(_id);

	// assign holiday group to selected employees
	const userIds = await getEmployeeIdsByAssignment(assignTo);
	// if(userIds.length === 0) {
	//   throw new NotFoundError("Employees not found in selected assignment");
	// }

	const holidayGroup = await HolidayGroup.findByIdAndUpdate(_id, {
		...req.body,
		companyId: company._id,
	});

	await Benefits.updateMany(
		{ userId: { $in: userIds } },
		{ $addToSet: { holidayGroups: holidayGroup._id } }
	);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(
				StatusCodes.OK,
				'Holiday group updated successfully',
				holidayGroup
			)
		);
};

const deleteHolidayGroup = async (req, res) => {
	const { holidayGroupIds } = req.body;

	if (!holidayGroupIds) {
		throw new BadRequestError('Please provide Holiday Group Ids');
	}

	const company = await CompanyDetails.findOne({ _id: req.user.companyId._id });
	if (!company) {
		throw new NotFoundError('Company Details not found');
	}

	const holidayGroups = await HolidayGroup.find({
		_id: { $in: holidayGroupIds },
		companyId: company._id,
		deleted: false,
	});
	if (holidayGroups.length === 0) {
		throw new NotFoundError('HolidayGroups not found');
	}

	await removeHolidayGroupFromEmployees(holidayGroupIds);
	await Holiday.updateMany(
		{ holidayGroupId: { $in: holidayGroupIds } },
		{ $set: { deleted: true, deletedAt: new Date() } }
	);
	await HolidayGroup.updateMany(
		{ _id: { $in: holidayGroupIds } },
		{ $set: { deleted: true, deletedAt: new Date() } }
	);

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, 'Holiday Groups deleted successfully')
		);
};

const getEmployeeIdsByAssignment = async (assignTo) => {
	let assignmentLabel;
	if (assignTo.label === 'employee') {
		assignmentLabel = 'userId';
	} else {
		assignmentLabel = assignTo.label;
	}
	const employmentDetailsQuery = {
		[assignmentLabel]: { $in: convertToObjectId(assignTo.value) },
		deleted: false,
	};

	const employees = await EmploymentDetails.find(employmentDetailsQuery).select(
		'userId'
	);
	return employees.map((emp) => emp.userId) || [];
};

const removeHolidayGroupFromEmployees = async (holidayGroupId) => {
	const holidayGroupIds = Array.isArray(holidayGroupId)
		? convertToObjectId(holidayGroupId)
		: [convertToObjectId(holidayGroupId)];

	await Benefits.updateMany(
		{ holidayGroups: { $in: holidayGroupIds } },
		{ $pull: { holidayGroups: { $in: holidayGroupIds } } }
	);
};

module.exports = {
	getHolidayGroups,
	addHolidayGroup,
	updateHolidayGroup,
	copyHolidayGroup,
	deleteHolidayGroup,
	getHolidayGroupDetails,
};
