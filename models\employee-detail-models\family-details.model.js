const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

const FamilyDetailsSchema = new mongoose.Schema(
	{
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
		maritalStatus: {
			type: String,
			enum: ['single', 'married', 'other', 'prefer-not-to-say'],
			required: [true, 'Please provide marital status'],
			index: true, // Optimized for faster queries
		},
		spouseName: {
			type: String,
			trim: true,
			required: function () {
				return this.maritalStatus === 'married';
			},
		},
		spouseEmploymentStatus: {
			type: String,
			enum: ['employed', 'unemployed', 'prefer-not-to-say', '', ``],
			required: function () {
				return this.maritalStatus === 'married';
			},
		},
		children: [
			{
				age: {
					type: Number,
					required: [true, 'Please provide age of the child'],
					max: [18, "Children can't be older than 18 years"],
				},
				name: {
					type: String,
					required: [true, 'Please provide name'],
					trim: true,
					minLength: [2, 'Name must be at least 2 characters long'],
					maxLength: [50, 'Name must be at most 50 characters long'],
				},
				dob: {
					type: Date,
					required: [true, 'Please provide date of birth'],
					// validate: {
					// 	validator: function (value) {
					// 		return value < new Date();
					// 	},
					// 	message: "Child's Date of birth must be in the past",
					// },
				},
				nationality: {
					type: String,
					required: [true, 'Please provide nationality'],
					trim: true,
					minLength: [4, 'Nationality must be at least 4 characters long'],
					maxLength: [50, 'Nationality must be at most 50 characters long'],
					match: [
						/^[a-zA-Z\s]+$/,
						'Nationality must contain only letters and spaces',
					],
				},
			},
		],
	},
	{ timestamps: true }
);

FamilyDetailsSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
FamilyDetailsSchema.plugin(aggregatePaginate);
FamilyDetailsSchema.plugin(autoPopulate);

module.exports = mongoose.model('FamilyDetails', FamilyDetailsSchema);
