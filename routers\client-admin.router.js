const express = require('express');
const router = express.Router();

// Middlewares
const {
	authenticationMiddleware,
	uploadImage,
	onboardingStepOneMiddleware,
	onboardingFinalStepMiddleware,
} = require('../middlewares');

// Controllers
const {
	onboardingStepOne,
	onboardingClientFinalStep,
} = require('../controllers/client-admin.controller');

/**
 * @route   POST /onboard
 * @desc    User onboarding step one (with logo upload)
 * @access  Private (requires authentication)
 */
router
	.route('/onboard')
	.post(
		authenticationMiddleware,
		uploadImage.single('logo'),
		onboardingStepOneMiddleware,
		onboardingStepOne
	);

/**
 * @route   PATCH /onboard
 * @desc    User onboarding final step
 * @access  Private (requires authentication)
 */
router
	.route('/onboard')
	.patch(
		authenticationMiddleware,
		onboardingFinalStepMiddleware,
		onboardingClientFinalStep
	);

module.exports = router;
