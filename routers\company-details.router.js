const express = require('express');
const router = express.Router();

// Controllers
const {
	getCompanyDetails,
	updateWorkParameters,
} = require('../controllers/company-details.controller');

// Middlewares
const { authenticationMiddleware } = require('../middlewares');

const {
	updateWorkParametersMiddleware,
} = require('../middlewares/company-details.middleware');

/**
 * @route   GET /
 * @desc    Get company details
 * @access  Private (Authenticated)
 */
router.route('/').get(authenticationMiddleware, getCompanyDetails);

/**
 * @route   PATCH /work-parameters
 * @desc    Update company work parameters
 * @access  Private (Authenticated)
 */
router
	.route('/work-parameters')
	.patch(
		authenticationMiddleware,
		updateWorkParametersMiddleware,
		updateWorkParameters
	);

module.exports = router;
