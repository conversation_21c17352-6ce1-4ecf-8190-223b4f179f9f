const express = require('express');
const { authenticationMiddleware } = require('../middlewares');
const {
	getMessages,
	markAsRead,
	sendMessage,
} = require('../controllers/message.controller');
const router = express.Router();

router.route('/').post(authenticationMiddleware, sendMessage);
router.route('/:chatId').get(authenticationMiddleware, getMessages);
router.route('/:messageId').patch(authenticationMiddleware, markAsRead);

module.exports = router;
