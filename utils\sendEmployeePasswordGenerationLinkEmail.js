const sendEmail = require('./sendEmail');
const {
	generateEmployeePasswordLinkTemplate,
} = require('./templates/employeePasswordGenerationLinkTemplate');

const sendEmployeePasswordGenerationLinkEmail = async ({
	origin,
	token,
	email,
	expirationTime,
	companyName,
}) => {
	return sendEmail({
		to: email,
		subject: 'Set Your Password',
		html: generateEmployeePasswordLinkTemplate({
			origin,
			token,
			email,
			expirationTime,
			companyName,
		}),
	});
};

module.exports = { sendEmployeePasswordGenerationLinkEmail };
