const mongoose = require('mongoose');

const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

const userFlagsSchema = new mongoose.Schema({
	userId: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'User',
	},
	isRegistrationComplete: {
		type: Boolean,
		default: false,
	},
	isPersonalDetailsComplete: {
		type: Boolean,
		default: false,
	},
	isFamilyDetailsComplete: {
		type: Boolean,
		default: false,
	},
	isQualificationDetailsComplete: {
		type: Boolean,
		default: false,
	},
	isSkillsDetailsComplete: {
		type: Boolean,
		default: false,
	},
	isContactDetailsComplete: {
		type: Boolean,
		default: false,
	},
	isEmploymentDetailsComplete: {
		type: Boolean,
		default: false,
	},
	isEarningsDetailsComplete: {
		type: Boolean,
		default: false,
	},
	isBenefitsDetailsComplete: {
		type: Boolean,
		default: false,
	},
	isClientRegistrationAsEmployeeComplete: {
		type: Boolean,
		default: false,
	},
});

userFlagsSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
userFlagsSchema.plugin(aggregatePaginate);
userFlagsSchema.plugin(autoPopulate);

module.exports = mongoose.model('UserFlags', userFlagsSchema);
