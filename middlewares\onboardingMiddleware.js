const { BadRequestError } = require('../errors');
const {
	onboardingStepOneSchema,
	onboardingFinalStepSchema,
} = require('../schemas/onboarding.schema');
const { imageMediaSchema } = require('./multerMiddleware');

const onboardingStepOneMiddleware = (req, res, next) => {
	const result = onboardingStepOneSchema.safeParse(req.body);
	if (req.file) {
		const mediaResult = imageMediaSchema.safeParse({
			mimetype: req.file?.mimetype,
			size: req.file?.size,
		});
		if (!mediaResult.success) {
			throw new BadRequestError(
				'Invalid Logo File',
				mediaResult.error.format()
			);
		}
	}

	if (!result.success) {
		throw new BadRequestError(
			'Please Provide Missing Fields',
			result.error.format()
		);
	}
	next();
};

const onboardingFinalStepMiddleware = (req, res, next) => {
	const result = onboardingFinalStepSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError(
			'Please Provide Missing Fields',
			result.error.format()
		);
	}
	next();
};

module.exports = { onboardingStepOneMiddleware, onboardingFinalStepMiddleware };
