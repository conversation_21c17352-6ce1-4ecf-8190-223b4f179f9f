const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const autoPopulate = require('mongoose-autopopulate');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const { BadRequestError, NotFoundError } = require('../../errors');
const { convertToObjectId } = require('../../utils/misc');

const BusinessUnitSchema = new mongoose.Schema({
	name: {
		type: String,
		required: true,
	},
	location: {
		type: String,
		index: true,
	},
	admin: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'User',
	},
	owner: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'User',
		required: [true, 'Please provide owner for business unit'],
	},
	companyId: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'CompanyDetails',

		required: [true, 'Please provide company details for business unit'],
	},
});

BusinessUnitSchema.pre(
	'updateMany',
	{ document: false, query: true },
	async function (next) {
		try {
			const filter = this.getFilter();
			const update = this.getUpdate();

			// Check if the update is attempting to set `deleted` to true
			if (update['$set']?.deleted === true) {
				// Ensure `_id` is an array of IDs
				// ...existing code...
				const businessUnitIds = filter._id.$in
					? filter._id.$in.map((id) => convertToObjectId(id))
					: [convertToObjectId(filter._id)];

				// Check if any departments exist for the given business units
				const hasDepartments = await mongoose.model('Department').exists({
					businessUnitId: { $in: businessUnitIds },
					deleted: false,
				});
				// Prevent deletion if departments exist
				if (hasDepartments) {
					return next(
						new BadRequestError(
							'Please delete all departments before deleting business unit'
						)
					);
				}
			}

			next();
		} catch (error) {
			next(error);
		}
	}
);
BusinessUnitSchema.plugin(aggregatePaginate);
BusinessUnitSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
BusinessUnitSchema.plugin(autoPopulate);
// TODO: Create a function to create default business unit during client onboarding

module.exports = mongoose.model('BusinessUnit', BusinessUnitSchema);
