const { StatusCodes } = require('http-status-codes');
const {
	NotFoundError,
	BadRequestError,
	UnauthorizedError,
} = require('../errors');
const { User, CompanyDetails, Country, Token } = require('../models');
const {
	attachCookiesToResponse,
	APIResponse,
	convertToObjectId,
	uploadFileToCloudinary,
} = require('../utils');
const { userRoles } = require('../models/user.model');
const {
	employeesListForOrganizationAggregation,
} = require('../db/aggregations/employee.aggregation');

const createCompany = async (req, res) => {
	const existingCompanyDetails = await CompanyDetails.findOne({
		registration: req.body.registration,
	});

	if (existingCompanyDetails) {
		throw new ConflictError('Company Details already exists');
	}

	const logoLocalPath = req.file?.path;
	if (!logoLocalPath) {
		throw new BadRequestError('Please provide logo', [
			'Logo local path not found',
		]);
	}

	const logoUrl = await uploadFileToCloudinary(logoLocalPath);
	if (!logoUrl) {
		throw new BadRequestError('Could not upload logo', ['Logo upload failed']);
	}

	const companyDetails = await CompanyDetails.create({
		owner: req.user.userId,
		clientAdmin: req.user.userId,
		logo: logoUrl,
		...req.body,
	});

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'success', companyDetails));
};

const changeCurrentCompany = async (req, res) => {
	const { companyId } = req.params;
	const glorifiedClientAdmin = await User.findOne({
		role: req.user.role,
		_id: req.user.userId,
	});

	// console.log('glorifiedClientAdmin:', glorifiedClientAdmin);
	if (!glorifiedClientAdmin) {
		throw new NotFoundError('User not found');
	}

	const company = await CompanyDetails.findOne({
		owner: glorifiedClientAdmin._id,
		_id: companyId,
	});

	// console.log('company:', company);
	if (!company) {
		throw new NotFoundError('Company with the given country was not found');
	}

	glorifiedClientAdmin.companyId = company._id;
	await glorifiedClientAdmin.save();

	// console.log('glorifiedClientAdmin after update:', glorifiedClientAdmin);

	let refreshToken;
	const existingToken = await Token.findOne({ user: glorifiedClientAdmin._id });
	// console.log('existingToken:', existingToken);
	if (!existingToken || !existingToken.isValid) {
		if (!existingToken.isValid) {
			throw new UnauthorizedError("Something's wrong, Please contact Support");
		}
	}
	refreshToken = existingToken.refreshToken;
	// console.log('refreshToken:', refreshToken);
	attachCookiesToResponse({ res, user: glorifiedClientAdmin, refreshToken });

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(StatusCodes.OK, `Switched to ${company.businessName}.`)
		);
};

const getAllCompanies = async (req, res) => {
	const page = parseInt(req.query.page) || 1;
	const limit = parseInt(req.query.limit) || 10;
	const options = { page, limit };
	console.log(req.params.countryId);
	const companiesAggregateQuery = CompanyDetails.aggregate([
		{
			$match: {
				owner: convertToObjectId(req.user.userId),
				// businessCountry: convertToObjectId(req.params.countryId),
			},
		},
		{
			$lookup: {
				from: 'countries',
				localField: 'businessCountry',
				foreignField: '_id',
				as: 'businessCountry',
			},
		},
		{
			$addFields: {
				businessCountry: {
					$first: '$businessCountry.name',
				},
			},
		},
	]);

	const result = await CompanyDetails.aggregatePaginate(
		companiesAggregateQuery,
		options
	);

	if (result?.docs?.length < 0) {
		throw new NotFoundError('No Companies found');
	}

	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'Success', {
			companies: result.docs,
			totalPages: result.totalPages,
			currentPage: result.page,
			totalResults: result.totalDocs,
		})
	);
};

const addClientAdmin = async (req, res) => {
	const { employeeId, companyId } = req.body;

	const employee = await User.findOne({ _id: employeeId, companyId });
	const company = await CompanyDetails.findOne({ _id: companyId });

	if (!employee) {
		throw new NotFoundError('Employee not found');
	}

	if (!companyId) {
		throw new NotFoundError('Company not found');
	}

	employee.role = userRoles.CLIENT_ADMIN;
	employee.clientAdminId = employee._id;
	company.clientAdmin = employee._id;

	await User.updateMany(
		{
			companyId: company._id,
		},
		{ $set: { clientAdminId: employee._id } }
	);

	await employee.save();
	await company.save();

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'New client admin appointed'));
};

const removeClientAdmin = async (req, res) => {
	const { clientAdminId, newClientAdminId } = req.body;

	// Check if old client admin exists and has correct role
	const clientAdmin = await User.findOne({
		_id: clientAdminId,
		role: userRoles.CLIENT_ADMIN,
	});
	if (!clientAdmin && newClientAdminId !== req.user.userId) {
		throw new NotFoundError(
			'Client admin not found or user is not a client admin'
		);
	}

	const company = await CompanyDetails.findOne({ clientAdmin: clientAdminId });
	if (!company) {
		throw new NotFoundError('Company not found');
	}

	// Set the new client admin ID based on request body or current user
	const newAdminId = newClientAdminId || req.user.userId;

	// If newClientAdminId provided, verify the user exists
	if (newClientAdminId) {
		const newAdmin = await User.findOne({ _id: newClientAdminId });
		if (!newAdmin) {
			throw new NotFoundError('New client admin user not found');
		}
	}

	// Update company details with new client admin
	company.clientAdmin = newAdminId;
	await company.save();

	// Update all employees under this client admin
	await User.updateMany(
		{ clientAdminId: clientAdminId, companyId: company._id },
		{ $set: { clientAdminId: newAdminId } }
	);

	if (clientAdmin && clientAdmin._id !== req.user.userId) {
		// Remove client admin role from old admin
		clientAdmin.role = userRoles.EMPLOYEE;
		clientAdmin.clientAdminId = newAdminId;
		await clientAdmin.save();
	}

	res
		.status(StatusCodes.OK)
		.json(
			new APIResponse(
				StatusCodes.OK,
				'Client admin removed and updated successfully'
			)
		);
};

const getAllEmployeesOfOrganization = async (req, res) => {
	const page = parseInt(req.query.page) || 1;
	const limit = parseInt(req.query.limit) || 10;
	const options = { page, limit };

	const employeesAggregateQuery = User.aggregate(
		employeesListForOrganizationAggregation(req.params.companyId)
	);

	const result = await User.aggregatePaginate(employeesAggregateQuery, options);
	if (result?.docs?.length < 0) {
		throw new NotFoundError('No Employees found');
	}

	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'Success', {
			employees: result.docs,
			totalPages: result.totalPages,
			currentPage: result.page,
			totalResults: result.totalDocs,
		})
	);
};

module.exports = {
	createCompany,
	changeCurrentCompany,
	getAllCompanies,
	addClientAdmin,
	removeClientAdmin,
	getAllEmployeesOfOrganization,
};
