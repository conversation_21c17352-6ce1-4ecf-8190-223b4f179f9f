const { Country } = require('../models');
const { NotFoundError } = require('../errors');
const { StatusCodes } = require('http-status-codes');
const {
	countriesAndCities,
	debouncedInputCities,
	allCountries,
	allCities,
	allCurrencies,
	allDialCodes,
	allCountryCities,
} = require('../db/aggregations/countries.aggregation');
const { APIResponse, convertToObjectId } = require('../utils');
const crypto = require('crypto');

const getAllCountries = async (req, res) => {
	const countries = await Country.aggregate(allCountries);

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'success', countries));
};

const getCountryById = async (req, res) => {
	const { id } = req.params;
	const country = await Country.findOne({ id: Number(id) });

	if (!country) {
		throw new NotFoundError(`No country found with id ${id}`);
	}

	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'success', country));
};

const getCountryCurrency = async (req, res) => {
	const { id } = req.params;
	const country = await Country.findOne({ id: Number(id) }).select(
		'id name currency currency_name currency_symbol'
	);

	if (!country) {
		throw new NotFoundError(`No country found with id ${id}`);
	}

	res.status(StatusCodes.OK).json(
		new APIResponse(StatusCodes.OK, 'success', {
			id: country.id,
			name: country.name,
			currency: country.currency,
			currency_name: country.currency_name,
			currency_symbol: country.currency_symbol,
		})
	);
};

// const getCountryCities = async (req, res) => {
// 	const { id } = req.params;
// 	console.log(` getCountryCities - id:`, id);

// 	const cities = await Country.aggregate(allCities(parseInt(id)));
// 	console.log(` getCountryCities - cities:`, cities);

// 	res
// 		.status(StatusCodes.OK)
// 		.json(new APIResponse(StatusCodes.OK, "success", cities[0].cities));
// };

const getSearchedCities = async (req, res) => {
	const { countryId: country, citySearchTerm: input } = req.query;

	const cities = await Country.aggregate(
		debouncedInputCities({ country, input })
	);
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'success', cities));
};

const getCountryCities = async (req, res) => {
	const countryName = req.params.countryName;

	const countryCities = await Country.aggregate(allCountryCities(countryName));
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'success', countryCities));
};

const getAllCurrencies = async (req, res) => {
	const currencies = await Country.aggregate(allCurrencies);
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'success', currencies));
};

const getAllDialCodes = async (req, res) => {
	const dialCodes = await Country.aggregate(allDialCodes);
	res
		.status(StatusCodes.OK)
		.json(new APIResponse(StatusCodes.OK, 'success', dialCodes));
};

module.exports = {
	getAllCountries,
	getCountryById,
	getCountryCities,
	getCountryCurrency,
	getCountryCities,
	getSearchedCities,
	getAllCurrencies,
	getAllDialCodes,
};
