Backend Refactor:
- Moved all routes to routers/index.js
- Refactor app.js
- Refactor auth.router.js
- Moved /show-me from user.router.js to auth.router.js
- Renamed /show-me to /me
- Renamed /users to /client-admin
- Renamed /onboarding to /onboard
- Renamed user.router.js to client-admin.router.js
- Refactor client-admin.router.js
- Refactor employee.router.js
- Created holiday-group.router.js
- Moved /holiday-group/:holidayGroupId from employee.router.js to holiday-group.router.js
- Renamed /holiday-group/:holidayGroupId to /:holidayGroupId/employees
- Moved /holiday-group/:holidayGroupId/remove/:employeeId from employee.router.js to holiday-group.router.js
- Renamed /holiday-group/:holidayGroupId/remove/:employeeId to /:holidayGroupId/employees/:employeeId
- Refactor country.router.js
- Moved /holiday-groups from company-details.router.js to holiday-group.router.js
- Renamed /holiday-groups to /
- Moved /holiday-groups/:id from company-details.router.js to holiday-group.router.js
- Renamed /holiday-groups/:id to /:id
- Moved /holiday-groups/copy from company-details.router.js to holiday-group.router.js
- Renamed /holiday-groups/copy to /copy
- Moved /holiday-groups/remove from company-details.router.js to holiday-group.router.js
- Renamed /holiday-groups/remove to /remove
- Created holiday.router.js
- Moved /holidays from company-details.router.js to holiday.router.js
- Renamed /holidays to /
- Moved /holidays/:holidayGroupId from company-details.router.js to holiday.router.js
- Renamed /holidays/:holidayGroupId to /:holidayGroupId
- Moved /holidays/remove from company-details.router.js to holiday.router.js
- Renamed /holidays/remove to /remove
- Created business-unit.router.js
- Moved /business-units from company-details.router.js to business-unit.router.js
- Renamed /business-units to /
- Moved /business-units/populate from company-details.router.js to business-unit.router.js
- Renamed /business-units/populate to /populate
- Moved /business-units/remove from company-details.router.js to business-unit.router.js
- Renamed /business-units/remove to /remove
- Moved /departments/:id from company-details.router.js to business-unit.router.js
- Renamed /departments/:id to /:id/departments
- Created department.router.js
- Moved /departments from company-details.router.js to department.router.js
- Renamed /departments to /
- Moved /departments/remove from company-details.router.js to department.router.js
- Renamed /departments/remove to /remove
- Moved /designations/:id from company-details.router.js to department.router.js
- Renamed /designations/:id to /:id/designations
- Created designation.router.js
- Moved /designations from company-details.router.js to designation.router.js
- Renamed /designations to /
- Moved /designations/remove from company-details.router.js to designation.router.js
- Renamed /designations/remove to /remove
- Refactor company-details.router.js
- Renamed /gClient to /glorified-client-admin
- Renamed /appoint-admin to /assign-admin
- Renamed /remove-admin to /unassign-admin
- Renamed / to /companies
- Refactor glorified-client-admin.router.js
- Refactor hr-module.router.js
- Refactor promote-client.router.js
- Renamed user.controller.js to client-admin.controller.js
- Created business-unit.controller.js
- Created department.controller.js
- Created designation.controller.js
- Created holiday.controller.js
- Created holiday-group.controller.js
- Renamed /gender-education-data to /gender-education
- Created module-admin.router.js
- Moved /module-admins from hr-module.router.js to module-admin.router.js
- Created module-admin.controller.js