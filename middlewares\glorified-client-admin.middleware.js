const { BadRequestError } = require('../errors');
const {
	createCompanySchema,
} = require('../schemas/glorified-client-admin.schema');
const { imageMediaSchema } = require('./multerMiddleware');

const createCompanyMiddleware = (req, res, next) => {
	// console.log(` createCompanyMiddleware - req:`, req.body);

	const result = createCompanySchema.safeParse(req.body);

	if (req.file) {
		const mediaResult = imageMediaSchema.safeParse({
			mimetype: req.file?.mimetype,
			size: req.file?.size,
		});
		if (!mediaResult.success) {
			throw new BadRequestError('Invalid File', mediaResult.error.format());
		}
	}

	if (!result.success) {
		throw new BadRequestError(
			'Please Provide Missing Fields',
			result.error.format()
		);
	}
	next();
};

module.exports = { createCompanyMiddleware };
