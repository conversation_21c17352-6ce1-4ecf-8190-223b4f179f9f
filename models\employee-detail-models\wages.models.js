const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

const WagesSchema = new mongoose.Schema({
	userId: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'User',
	},
	name: {
		type: String,
	},
	amount: {
		type: Number,
	},
	cpfApplicable: {
		type: Boolean,
		default: false,
	},
	taxApplicable: {
		type: Boolean,
		default: false,
	},
	type: {
		type: String,
		enum: ['OTHER_WAGES', 'ADDITIONAL_WAGES'],
	},
});

WagesSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
WagesSchema.plugin(aggregatePaginate);
WagesSchema.plugin(autoPopulate);

module.exports = mongoose.model('Wages', WagesSchema);
