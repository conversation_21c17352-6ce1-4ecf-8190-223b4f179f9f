const { BadRequestError } = require('../errors');
const {
	countrySchema,
	debouncedInputCitiesSchema,
} = require('../schemas/country.schema');

const validateCountryId = async (req, res, next) => {
	const { id } = req.query;
	if (!id || isNaN(Number(id))) {
		throw new BadRequestError('Please provide a valid country ID');
	}
	next();
};

const validateCountryData = async (req, res, next) => {
	try {
		await countrySchema.parseAsync(req.body);
		next();
	} catch (error) {
		throw new BadRequestError(error.errors[0].message);
	}
};

const validateDebouncedInputCities = (req, res, next) => {
	const result = debouncedInputCitiesSchema.safeParse(req.query);
	if (!result.success) {
		throw new BadRequestError(
			'Please provide a valid country name and city input',
			result.error.format()
		);
	}
	next();
};

module.exports = {
	validateCountryId,
	validateCountryData,
	validateDebouncedInputCities,
};
