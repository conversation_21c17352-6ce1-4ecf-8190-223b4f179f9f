const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

const ProjectSchema = new mongoose.Schema(
	{
		name: {
			type: String,
			required: [true, 'Project name is required'],
			unique: true,
			lowercase: true,
		},
		code: {
			type: String,
			required: [true, 'Project code is required'],
			unique: true,
		},
		description: {
			type: String,
			default: '',
		},
		status: {
			type: String,
			enum: ['active', 'inactive'],
			default: 'active',
		},
		lead: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
			autopopulate: {
				select: 'name email profilePhoto',
			},
			required: [true, 'Project lead is required'],
		},
		bgType: {
			type: String,
			enum: ['color', 'image'],
			default: 'color',
		},
		bgImage: {
			url: {
				type: String,
			},
			thumbnail: {
				type: String,
			},
		},
		bgColor: {
			type: String,
		},
		companyId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'CompanyDetails',
			required: true,
		},
		createdBy: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
	},
	{ timestamps: true }
);

ProjectSchema.plugin(autoPopulate);
ProjectSchema.plugin(aggregatePaginate);
ProjectSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});

module.exports = mongoose.model('Project', ProjectSchema);
