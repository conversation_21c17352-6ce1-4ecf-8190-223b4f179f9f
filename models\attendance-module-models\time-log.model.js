const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

const userAttendanceStatus = {
	clockIn: 'inOffice',
	clockOut: 'clockOut',
	break: 'onBreak',
};

const TimeLogSchema = new mongoose.Schema(
	{
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
		attendanceTime: [
			{
				type: {
					type: String,
					enum: ['clockIn', 'clockOut', 'start-break', 'end-break'],
				},
				time: {
					type: Date,
				},
			},
		],
		actualWorkingHoursInMinutes: {
			type: Number, // in minutes
		},
		overtimeWorkingHours: {
			type: Number,
		},
		// this will be used to calculate the overtime
		overtimeAttendanceTime: [
			{
				type: {
					type: String,
					enum: ['clockIn', 'clockOut', 'start-break', 'end-break'],
				},
				time: {
					type: Date,
				},
			},
		],

		// Values fetched form other models for simplicity
		// from employment details
		isOvertimeEligible: {
			type: Boolean,
			default: false,
		},
		expectedWorkingHours: {
			type: Number,
		},
		// from shift setting
		startTime: {
			type: String,
		},
		endTime: {
			type: String,
		},
		clockInLimit: {
			type: Number,
			default: 1,
		},
		clockOutLimit: {
			type: Number,
			default: 1,
		},
		clockInDelay: {
			type: Number,
			default: 15,
		},
		clockOutDelay: {
			type: Number,
			default: 120,
		},
		breakLimit: {
			type: Number,
			default: 1,
		},
		lateInMinutes: {
			type: Number,
		},
		earlyOutMinutes: {
			type: Number,
		},
		approvalStatus: {
			type: String,
			enum: ['pending', 'approved', 'rejected', 'not-required'],
			default: 'not-required',
		},
		approvedBy: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
		approvedAt: {
			type: Date,
		},
		reasonForRejection: {
			type: String,
		},
		clockOutBy: {
			type: String,
			enum: ['user', 'admin', 'system'],
		},
		projectId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'Project',
		},
	},
	{ timestamps: true }
);

TimeLogSchema.plugin(autoPopulate);
TimeLogSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
TimeLogSchema.plugin(aggregatePaginate);

module.exports = mongoose.model('TimeLog', TimeLogSchema);
