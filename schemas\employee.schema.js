const { z } = require('zod');
const {
	imageMediaSchema,
	imageAndPdfMediaSchema,
} = require('../middlewares/multerMiddleware');
const {
	qualificationTypes,
} = require('../models/employee-detail-models/education-details.model');

const dayjs = require('dayjs');

const personalDetailsSchema = z
	.object({
		employeeOrgId: z
			.string()
			.nonempty('Employee ID is required')
			.min(6, 'Employee ID must be at least 6 characters long'),

		email: z
			.string()
			.nonempty('Email is required')
			.email('Invalid email format'),

		// TODO: Password wil get submitted in the final submission
		// password: z
		// 	.string()
		// 	.min(8, "Password must be at least 8 characters long")
		// 	.max(100, "Password must be at most 100 characters long"),

		nameOnNRIC: z
			.string()
			.nonempty('Full Name as per NRIC is required')
			.min(3, 'Full Name must be at least 3 characters long')
			.max(50, 'Full Name must be at most 50 characters long')
			.regex(/^[a-zA-Z\s]+$/, 'Full Name must contain only letters and spaces'),

		countryDialCode: z.string().nonempty('Country dial code is required'),

		mobile: z
			.string()
			.nonempty('Mobile number is required')
			.min(8, 'Mobile number must be at least 8 digits long')
			.max(15, 'Mobile number must be at most 15 digits long')
			.regex(/^\d+$/, 'Mobile number must contain only numbers'),

		gender: z.enum(['male', 'female', 'other'], {
			errorMap: () => ({
				message: 'Gender must be either male, female, or other',
			}),
		}),

		dateOfJoining: z
			.string()
			.regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')
			.transform((val) => new Date(val)),

		dob: z
			.string()
			.regex(
				/^\d{4}-\d{2}-\d{2}$/,
				'Date of Birth must be in YYYY-MM-DD format'
			)
			.transform((val) => new Date(val)),

		age: z
			.string()
			.refine((val) => {
				const age = parseInt(val);
				return !isNaN(age) && age >= 18 && age <= 100;
			})
			.optional(),

		nationality: z
			.string()
			.nonempty('Nationality is required')
			.regex(/^[a-f\d]{24}$/i, 'Invalid Nationality ID format'),

		residentialStatus: z.enum(
			[
				'Singapore Citizen',
				'Singapore PR',
				'Employment Pass',
				'SPass',
				'Work Permit',
				'LOC',
			],
			{
				errorMap: () => ({
					message:
						'Residential Status must be one of: Singapore Citizen, Singapore PR, Employment Pass, SPass, Work Permit, or LOC',
				}),
			}
		),

		icFinPrefix: z.string().optional(),

		icFinNumber: z
			.string()
			.nonempty('IC/FIN Number is required')
			.min(9, 'IC/FIN Number must be exactly 9 characters long')
			.max(9, 'IC/FIN Number must be exactly 9 characters long')
			.regex(
				/^[STFGM][0-9]{7}[A-Z]$/,
				'IC/FIN number must be exactly 9 characters long with first and last character in upper case and remaining seven between the uppercase alphabets will be numerical'
			),

		issueDate: z
			.string()
			// .regex(/^\d{4}-\d{2}-\d{2}$/, "Issue Date must be in YYYY-MM-DD format")
			.transform((val) => dayjs(val))
			.optional(),

		expiryDate: z
			.string()
			.transform((val) => dayjs(val))
			.optional(),

		expiryDateReminder: z.string().optional(),

		religion: z
			.string()
			.nonempty('Religion is required')
			.min(2, 'Religion must be at least 2 characters long')
			.max(50, 'Religion must be at most 50 characters long')
			.regex(/^[a-zA-Z\s]+$/, 'Religion must contain only letters and spaces'),

		race: z
			.string()
			.nonempty('Race is required')
			.min(2, 'Race must be at least 2 characters long')
			.max(50, 'Race must be at most 50 characters long')
			.regex(
				/^[a-zA-Z\s-]+$/,
				'Race must contain only letters, spaces, and dashes'
			),

		country: z
			.string()
			.nonempty('Country is required in address')
			.regex(/^[a-f\d]{24}$/i, 'Invalid Country ID format'),

		postalCode: z
			.string()
			.nonempty('Postal Code is required')
			.regex(/^\d{6}$/, 'Postal Code must be exactly 6 digits'),

		streetName: z
			.string()
			.nonempty('Street Name is required')
			.min(2, 'Street Name must be at least 2 characters long')
			.max(100, 'Street Name must be at most 100 characters long'),

		houseNo: z
			.string()
			.nonempty('House Number is required')
			.min(1, 'House Number must be at least 1 character long')
			.max(10, 'House Number must be at most 10 characters long'),

		levelNo: z
			.string()
			.min(1, 'Level Number must be at least 1 character long')
			.max(5, 'Level Number must be at most 5 characters long')
			.optional(),

		unitNo: z
			.string()
			.min(1, 'Unit Number must be at least 1 character long')
			.max(5, 'Unit Number must be at most 5 characters long')
			.optional(),

		address: z
			.string()
			.nonempty('Address is required')
			.min(5, 'Address must be at least 5 characters long')
			.max(200, 'Address must be at most 200 characters long')
			.regex(
				/^[a-zA-Z0-9\s,.\-#]+$/,
				'Address must contain only letters, numbers, spaces, commas, periods, hashtags and hyphens'
			),

		// Family Details schema merged with personal details below
		maritalStatus: z.enum(['single', 'married', 'other', 'prefer-not-to-say'], {
			errorMap: () => ({
				message:
					'Marital Status must be one of: single, married, other, prefer-not-to-say',
			}),
		}),

		spouseName: z.string().optional(),

		spouseEmploymentStatus: z
			.enum(['employed', 'unemployed', 'prefer-not-to-say', '', ``], {
				errorMap: () => ({
					message:
						'Employment Status must be one of: employed, unemployed, prefer-not-to-say',
				}),
			})
			.optional(),

		children: z
			.array(
				z.object({
					name: z.string().nonempty('Name is required'),

					dob: z
						.string()
						.regex(
							/^\d{4}-\d{2}-\d{2}$/,
							'Date of Birth must be in YYYY-MM-DD format'
						),

					age: z.number().min(0, 'Age must be a positive number').optional(),

					nationality: z
						.string()
						.nonempty('Nationality is required')
						.min(4, 'Nationality must be at least 4 characters long')
						.max(50, 'Nationality must be at most 50 characters long')
						.regex(
							/^[a-zA-Z\s]+$/,
							'Nationality must contain only letters and spaces'
						),
				})
			)
			.optional(),

		profilePhoto: imageMediaSchema.optional(),
	})
	.strict()
	.superRefine((data, ctx) => {
		if (data.maritalStatus === 'married') {
			if (!data.spouseName) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: "Spouse Name is required when marital status is 'married'",
					path: ['spouseName'],
				});
			}
			if (!data.spouseEmploymentStatus) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message:
						"Spouse Employment Status is required when marital status is 'married'",
					path: ['spouseEmploymentStatus'],
				});
			}
		}
	});

const qualificationsSchema = z
	.object({
		employeeId: z
			.string()
			.nonempty('Employee ID is required')
			.regex(/^[a-f\d]{24}$/i, 'Invalid Employee ID format'),
		educationalDetails: z.array(
			z
				.object({
					instituteName: z.string().max(100).optional(),
					qualification: z.enum(
						['UNDER_GRADUATE', 'POST_GRADUATE', 'NO_FORMAL_EDUCATION'],
						{
							errorMap: () => ({
								message: 'Select qualification',
							}),
						}
					),
					grade: z.string().max(50).optional(),
					startDate: z.string(),
					endDate: z.string(),
					document: imageAndPdfMediaSchema.optional(),
				})
				.superRefine((data, ctx) => {
					if (
						data.startDate &&
						data.endDate &&
						data.startDate >= data.endDate
					) {
						ctx.addIssue({
							code: z.ZodIssueCode.custom,
							message: 'End Date must be after Start Date',
							path: ['endDate'],
						});
					}

					// Required fields if qualification is not 'NO_FORMAL_EDUCATION'
					if (
						data.qualification &&
						data.qualification !== 'NO_FORMAL_EDUCATION'
					) {
						if (!data.instituteName) {
							ctx.addIssue({
								code: z.ZodIssueCode.custom,
								message: 'Institute name is required',
								path: ['instituteName'],
							});
						}
						if (!data.grade) {
							ctx.addIssue({
								code: z.ZodIssueCode.custom,
								message: 'Grade is required',
								path: ['grade'],
							});
						}
						if (!data.startDate) {
							ctx.addIssue({
								code: z.ZodIssueCode.custom,
								message: 'Start date is required',
								path: ['startDate'],
							});
						}
						if (!data.endDate) {
							ctx.addIssue({
								code: z.ZodIssueCode.custom,
								message: 'End date is required',
								path: ['endDate'],
							});
						}
					}

					// Validate date format (YYYY-MM-DD)
					const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
					if (data.startDate && !dateRegex.test(data.startDate)) {
						ctx.addIssue({
							code: z.ZodIssueCode.custom,
							message: 'Start date must be in the format YYYY-MM-DD',
							path: ['startDate'],
						});
					}

					if (data.endDate && !dateRegex.test(data.endDate)) {
						ctx.addIssue({
							code: z.ZodIssueCode.custom,
							message: 'End date must be in the format YYYY-MM-DD',
							path: ['endDate'],
						});
					}
				})
		),

		hardSkills: z.array(z.string().min(2).max(50)).default([]),

		softSkills: z.array(z.string().min(2).max(50)).default([]),

		experienceDetails: z
			.array(
				z.object({
					location: z.string().nonempty('Location is required'),
					companyName: z.string().nonempty('Company Name is required'),
					designation: z.string().nonempty('Designation is required'),
					periodFrom: z
						.string()
						.regex(
							/^\d{4}-\d{2}-\d{2}$/,
							'Start Date must be in YYYY-MM-DD format'
						),
					periodTo: z
						.string()
						.regex(
							/^\d{4}-\d{2}-\d{2}$/,
							'End Date must be in YYYY-MM-DD format'
						),
					reasonForLeaving: z.string().optional(),
				})
			)
			.optional(),
	})
	.strict();

const contactDetailsSchema = z
	.object({
		employeeId: z.string().nonempty('Employee ID is required'),
		contacts: z
			.array(
				z
					.object({
						type: z.enum(['reference', 'emergency'], {
							errorMap: () => ({
								message:
									"Contact type must be either 'reference' or 'emergency'",
							}),
						}),

						name: z
							.string()
							.min(2, 'Name must be at least 2 characters long')
							.max(50, 'Name must be at most 50 characters long')
							.optional(),

						countryDialCode: z
							.string()
							.nonempty('Country dial code is required'),

						relationship: z
							.string()
							.nonempty('Relationship is required')
							.min(2, 'Relationship must be at least 2 characters long')
							.max(50, 'Relationship must be at most 50 characters long'),

						phone: z
							.string()
							.nonempty('Phone number is required')
							.min(8, 'Phone number must be at least 8 digits long')
							.max(15, 'Phone number must be at most 15 digits long')
							.regex(/^\d+$/, 'Phone number must contain only numbers'),

						email: z.string().email('Invalid email format').optional(),
					})
					.superRefine((data, ctx) => {
						if (data.type === 'emergency' && !data.name) {
							ctx.addIssue({
								code: z.ZodIssueCode.custom,
								message: 'Name is required for emergency contacts',
								path: ['name'],
							});
						}
						if (data.type === 'reference' && !data.email) {
							ctx.addIssue({
								code: z.ZodIssueCode.custom,
								message: 'Email is required for reference contacts',
								path: ['email'],
							});
						}
					})
			)
			.min(1, 'At least one contact is required'),
	})
	.strict();

const equipmentSchema = z
	.object({
		equipmentName: z
			.string()
			.trim()
			.min(2, 'Equipment name must be at least 2 characters long')
			.max(50, 'Equipment name must be at most 50 characters long'),
		brand: z
			.string()
			.trim()
			.min(2, 'Brand must be at least 2 characters long')
			.max(50, 'Brand must be at most 50 characters long'),
		model: z
			.string()
			.trim()
			.min(2, 'Model must be at least 2 characters long')
			.max(50, 'Model must be at most 50 characters long'),
		serialNumber: z.string().nonempty('Serial number is required'),
		issueDate: z
			.string()
			.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid issue date format'),
		returnDate: z.string().optional(),
		issueReason: z.enum(['new-hire', 'replacement', 'repair', 'other']),
		otherIssueReason: z.string().optional(),
		returnReason: z
			.enum(['damaged', 'end-of-contract', 'upgrade', 'other'])
			.optional(),
		otherReturnReason: z.string().optional(),
		assetTag: z.string().nonempty('Asset tag is required'),
	})
	.superRefine((data, ctx) => {
		if (data.issueReason === 'other' && !data.otherIssueReason) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'Please specify the other issue reason',
				path: ['otherIssueReason'],
			});
		}
		if (data.returnReason === 'other' && !data.otherReturnReason) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'Please specify the other return reason',
				path: ['otherReturnReason'],
			});
		}
	});

const employmentDetailsSchema = z
	.object({
		employeeId: z
			.string()
			.nonempty('Employee ID is required')
			.regex(/^[a-f\d]{24}$/i, 'Invalid Employee ID format'),
		source: z.enum(['staff-recommendation', 'job-advertisement'], {
			errorMap: () => ({
				message:
					"Source must be either 'staff-recommendation' or 'job-advertisement'",
			}),
		}),

		businessUnit: z
			.string()
			.nonempty('Business unit is required')
			.min(2, 'Business unit must be at least 2 characters long')
			.max(100, 'Business unit must be at most 100 characters long'),

		department: z
			.string()
			.nonempty('Department is required')
			.min(2, 'Department must be at least 2 characters long')
			.max(100, 'Department must be at most 100 characters long'),

		designation: z
			.string()
			.nonempty('Designation is required')
			.min(2, 'Designation must be at least 2 characters long')
			.max(100, 'Designation must be at most 100 characters long'),

		reportingTo: z
			.string()
			.nonempty('Reporting manager ID is required')
			.min(24, 'Invalid MongoDB Object ID')
			.max(24, 'Invalid MongoDB Object ID')
			.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID'),

		employeeRole: z.enum(['3', '4', '5', '6', '10'], {
			errorMap: () => ({
				message:
					'Role should not be other than Admin, Business Unit Head, Department Head or Employee.',
			}),
		}),

		probationPeriod: z.string().refine(
			(val) => {
				const num = parseInt(val, 10);
				return !isNaN(num) && num >= 0 && num <= 12;
			},
			{
				message: 'Probation period must be an integer between 0 and 12',
			}
		),
		employmentType: z.enum(['part-time', 'full-time'], {
			errorMap: () => ({
				message: "Employment type must be either 'part-time' or 'full-time'",
			}),
		}),

		workSchedule: z
			.enum(['shifts', 'generic'], {
				errorMap: () => ({
					message: "Work schedule must be either 'shifts' or 'generic'",
				}),
			})
			.optional(),

		shiftId: z
			.string()
			.nonempty('Shift ID is required')
			.min(24, 'Invalid MongoDB Object ID')
			.max(24, 'Invalid MongoDB Object ID')
			.regex(/^[a-f\d]{24}$/, 'Invalid MongoDB Object ID'),

		// workingDays: z
		// 	.enum(['5_DAYS', '5.5_DAYS', '6_DAYS', 'ALTERNATE_SATURDAYS'], {
		// 		errorMap: () => ({
		// 			message: 'Working Days invalid',
		// 		}),
		// 	})
		// 	.optional(),

		// workingHours: z
		// 	.enum(['4', '6', '8', '10', '12'], {
		// 		errorMap: () => ({
		// 			message: 'Please provide valid working hours',
		// 		}),
		// 	})
		// 	.optional(),

		// firstOffDay: z
		// 	.enum(
		// 		[
		// 			'SUNDAY',
		// 			'MONDAY',
		// 			'TUESDAY',
		// 			'WEDNESDAY',
		// 			'THURSDAY',
		// 			'FRIDAY',
		// 			'SATURDAY',
		// 		],
		// 		{
		// 			errorMap: () => ({
		// 				message: 'Please select valid day.',
		// 			}),
		// 		}
		// 	)
		// 	.optional(),
		// secondOffDay: z
		// 	.enum(
		// 		[
		// 			'SUNDAY',
		// 			'MONDAY',
		// 			'TUESDAY',
		// 			'WEDNESDAY',
		// 			'THURSDAY',
		// 			'FRIDAY',
		// 			'SATURDAY',
		// 		],
		// 		{
		// 			errorMap: () => ({
		// 				message: 'Please select valid day.',
		// 			}),
		// 		}
		// 	)
		// 	.optional(),
		// halfDay: z
		// 	.enum(['FIRST_HALF', 'SECOND_HALF'], {
		// 		errorMap: () => ({
		// 			message: 'Please select either first half or second half',
		// 		}),
		// 	})
		// 	.optional(),

		isBlocked: z.boolean().default(false).optional(),

		overTimeEligible: z.boolean().default(false),
		equipment: z
			.array(equipmentSchema)
			.max(3, 'At most three equipment items allowed')
			.optional(),
	})
	.strict();
// .superRefine((data, ctx) => {
// 	if (data.employmentType === 'full-time' && data.workSchedule === 'shifts') {
// 		if (!data.workingDays) {
// 			ctx.addIssue({
// 				code: z.ZodIssueCode.custom,
// 				message:
// 					'Working days is required when working full time and schedule is shifts',
// 				path: ['workingDays'],
// 			});
// 		}

// 		if (!data.workingHours) {
// 			ctx.addIssue({
// 				code: z.ZodIssueCode.custom,
// 				message:
// 					'Working hours is required when working full time and schedule is shifts',
// 				path: ['workingHours'],
// 			});
// 		}
// 	}

// 	if (
// 		data.employmentType === 'full-time' &&
// 		data.workSchedule === 'generic'
// 	) {
// 		if (!data.workingDays) {
// 			ctx.addIssue({
// 				code: z.ZodIssueCode.custom,
// 				message:
// 					'Working days is required when working full time and schedule is generic',
// 				path: ['workingDays'],
// 			});
// 		}

// 		if (!data.workingHours) {
// 			ctx.addIssue({
// 				code: z.ZodIssueCode.custom,
// 				message:
// 					'Working hours is required when working full time and schedule is generic',
// 				path: ['workingHours'],
// 			});
// 		}
// 	}

// 	if (data.workingDays === '5.5_DAYS') {
// 		if (!data.halfDay) {
// 			ctx.addIssue({
// 				code: z.ZodIssueCode.custom,
// 				message: 'Half day is required if working 5.5 days',
// 				path: ['halfDay'],
// 			});
// 		}
// 	}

// 	if (
// 		data.workingDays === '5_DAYS' ||
// 		data.workingDays === '4_DAYS' ||
// 		data.workingDays === '3_DAYS' ||
// 		data.workingDays === '2_DAYS' ||
// 		data.workingDays === '1_DAY'
// 	) {
// 		if (!data.secondOffDay && data.workSchedule === 'generic') {
// 			ctx.addIssue({
// 				code: z.ZodIssueCode.custom,
// 				message: 'Second off day is required if working lesser than 5.5 days',
// 				path: ['secondOffDay'],
// 			});
// 		}
// 	}
// });

const earningsDetailsSchema = z
	.object({
		basicPay: z.object({
			currency: z.string().nonempty('Currency for basic pay is required'),
			amount: z.number().positive('Basic pay cannot be negative'),
		}),
		paymentMode: z.enum(['cash/cheque', 'bank']),
		frequency: z.enum(['daily', 'weekly', 'monthly']),
		payBasis: z.enum(['hourly', 'daily', 'weekly', 'monthly']),
		dailyRate: z.number().positive('Daily rate cannot be negative'),
		hourlyRate: z.number().positive('Hourly rate cannot be negative'),
		weeklyRate: z.number().positive('Weekly rate cannot be negative'),
		yearlyRate: z.number().positive('Yearly rate cannot be negative'),
		overtimeRate: z.number('Overtime rate cannot be negative'),
		isSalaryAdvanceEligible: z.boolean().default(false),
		salaryAdvance: z
			.number()
			.nonnegative('Salary advance cannot be negative')
			.optional(),
		bankName: z
			.string()
			.max(100, 'Bank name must be under 100 characters')
			.optional(),
		accountNumber: z
			.string()
			.min(6, 'Account number must be at least 6 digits')
			.max(20, "Account number can't exceed 20 digits")
			.regex(/^\d+$/, 'Account number must contain only numbers')
			.optional(),
		accountHolderName: z
			.string()
			.min(2, 'Account holder name must be at least 2 characters')
			.max(100, 'Account holder name must be less than 100 characters')
			.optional(),
		bankCode: z
			.string()
			.min(3, 'Bank code must be at least 3 characters')
			.max(15, 'Bank code must be under 15 characters')
			.optional(),
		swiftBIC: z
			.string()
			.regex(
				/^[A-Z0-9]{8,11}$/,
				'SWIFT/BIC must be 8 or 11 alphanumeric characters'
			)
			.optional(),
		branchCode: z
			.string()
			.min(3, 'Branch code must be at least 3 characters')
			.max(15, 'Branch code must be under 15 characters')
			.optional(),
	})
	.superRefine((data, ctx) => {
		if (data.isSalaryAdvanceEligible && !data.salaryAdvance) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'Salary advance is required',
				path: ['salaryAdvance'],
			});
		}
		if (data.paymentMode === 'bank') {
			const bankFields = [
				'bankName',
				'accountNumber',
				'accountHolderName',
				'bankCode',
				'swiftBIC',
				'branchCode',
			];

			bankFields.forEach((field) => {
				if (!data[field]) {
					ctx.addIssue({
						code: z.ZodIssueCode.custom,
						message: `${field} is required when payment mode is bank`,
						path: [field],
					});
				}
			});
		}
	});

const benefitsSchema = z
	.object({
		employeeId: z.string().nonempty('Employee ID is required'),
		holiday: z
			.object({
				eligibleForOffInLieu: z.boolean().default(false),
				assigned: z
					.array(z.string().regex(/^[a-fA-F0-9]{24}$/, 'Invalid ObjectId'))
					.optional(),
			})
			.optional(),
		leave: z
			.object({
				eligibleForOffInLieu: z.boolean().default(false),
				code: z.string().optional(),
				type: z.string().optional(),
				eligibility: z.string().optional(),
				leaveIncrement: z
					.number()
					.min(0, 'Leave increment must be positive')
					.optional(),
				hourlyTimeOff: z.boolean().default(false),
				prorate: z.boolean().default(false),
			})
			.optional(),
		health: z
			.object({
				eligibleForOffInLieu: z.boolean().default(false),
				assigned: z
					.array(z.string().regex(/^[a-fA-F0-9]{24}$/, 'Invalid ObjectId'))
					.optional(),
			})
			.optional(),
	})
	.strict();

const offboardingFormSchema = z
	.object({
		dateOfLeave: z
			.string()
			.regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')
			.transform((val) => new Date(val)),
		reason: z
			.string()
			.nonempty('Reason for leave is needed')
			.max(200, 'Reason should not be more than 200 characters long'),
		exitInterviewConducted: z.boolean().default(false),
		interviewConductedBy: z
			.string()
			.nonempty('Employee ID is required')
			.regex(/^[a-f\d]{24}$/i, 'Invalid Employee ID format'),
	})
	.superRefine((data, ctx) => {
		if (data.exitInterviewConducted && data.interviewConductedBy === '') {
			ctx.addIssue({
				path: ['interviewConductedBy'],
				message: 'Employee ID is required',
			});
		}
	});

const onboardEmployeeLinkSchema = z
	.object({
		employeeDetails: z
			.array(
				z
					.object({
						name: z
							.string()
							.max(100, 'Name should not be more than 100 characters long')
							.nonempty('Name is required'),
						email: z
							.string()
							.email('Invalid email format')
							.nonempty('Email is required'),
						companyId: z
							.string()
							.regex(/^[a-f\d]{24}$/i, 'Invalid Company ID format')
							.nonempty('Company ID is required'),
						clientAdminId: z
							.string()
							.regex(/^[a-f\d]{24}$/i, 'Invalid Client Admin ID format')
							.nonempty('Client Admin ID is required'),
					})
					.strict()
			)
			.max(50, 'Maximum of 50 employees can be onboarded at a time'),
	})
	.strict();

const verifyLinkTokenSchema = z
	.object({
		token: z.string().nonempty('Token is required'),
	})
	.strict();

module.exports = {
	personalDetailsSchema,
	contactDetailsSchema,
	employmentDetailsSchema,
	qualificationsSchema,
	earningsDetailsSchema,
	benefitsSchema,
	offboardingFormSchema,
	onboardEmployeeLinkSchema,
	verifyLinkTokenSchema,
};
