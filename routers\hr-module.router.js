const express = require('express');
const router = express.Router();

// Middlewares
const {
	authorizePermissions,
	authenticationMiddleware,
} = require('../middlewares');

// Controllers
const {
	getGenderAndEducationData,
} = require('../controllers/hr-module.controller');

// Models
const { userRoles } = require('../models/user.model');

/**
 * @route   GET /gender-education-data
 * @desc    Fetch gender and education data for the HR module
 * @access  Private (GLORIFIED_CLIENT_ADMIN, CLIENT_ADMIN)
 */
router
	.route('/gender-education')
	.get(
		authenticationMiddleware,
		authorizePermissions(
			userRoles.GLORIFIED_CLIENT_ADMIN,
			userRoles.CLIENT_ADMIN
		),
		getGenderAndEducationData
	);

module.exports = router;
