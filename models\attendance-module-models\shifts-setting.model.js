const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

const ShiftSettingSchema = new mongoose.Schema(
	{
		type: {
			type: String,
			enum: ['generic', 'roster'],
			default: 'generic',
		},
		isDefault: {
			type: Boolean,
			default: false,
		},
		companyId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'CompanyDetails',
			required: true,
		},
		code: {
			type: String,
			required: true,
			trim: true,
			required: function () {
				if (this.isDefault) {
					return false;
				}
				return true;
			},
		},
		name: {
			type: String,
			required: true,
			lowercase: true,
			trim: true,
		},
		isApprovalStatusEnabled: {
			type: Boolean,
			default: false,
		},
		approvalFrequency: {
			type: String,
			enum: ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'],
			default: 'daily',
		},
		startTime: {
			type: String,
			required: true, // Expected format "HH:MM"
		},
		endTime: {
			type: String,
			required: true, // Expected format "HH:MM"
		},
		clockInLimit: {
			type: Number,
			default: 1,
		},
		clockOutLimit: {
			type: Number,
			default: 1,
		},
		breakLimit: {
			type: Number,
			default: 3,
		},
		clockInDelay: {
			type: Number,
			default: 15,
		},
		clockOutDelay: {
			type: Number,
			default: 120,
		},
		tardinessMinutes: {
			type: Number,
			default: 0,
		},
		tardinessMinutesPerMonth: {
			type: Number,
			default: 0,
		},
		workLocationCheckEnabled: {
			type: Boolean,
			default: false,
		},
		weekStartsFrom: {
			type: String,
			enum: [
				'sunday',
				'monday',
				'tuesday',
				'wednesday',
				'thursday',
				'friday',
				'saturday',
			],
			default: 'monday',
		},
		allowedAttendanceDays: {
			type: [String],
			enum: [
				'monday',
				'tuesday',
				'wednesday',
				'thursday',
				'friday',
				'saturday',
				'sunday',
			],
			default: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
		},
		halfDayMark: {
			type: String, // Optional: e.g. "13:00"
		},
		maxLoginHours: {
			type: Number,
		},
		minLoginHours: {
			type: Number,
		},
		maxClockInTime: {
			type: Number,
		},
		workLocation: [
			{
				latitude: Number,
				longitude: Number,
				radiusInMeters: Number,
			},
		],
		wifiCheckEnabled: {
			type: Boolean,
			default: false,
		},
		OfficeWifiIPAddress: [String],
		qrClockInCheckEnabled: {
			type: Boolean,
			default: false,
		},
		qrClockInToken: {
			type: String,
		},
		facialCheckEnabled: {
			type: Boolean,
			default: false,
		},
	},
	{ timestamps: true }
);

ShiftSettingSchema.plugin(autoPopulate);
ShiftSettingSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
ShiftSettingSchema.plugin(aggregatePaginate);

const ShiftSettings = mongoose.model('Shift', ShiftSettingSchema);
const generateDefaultShiftSetting = async (data) => {
	const { companyId } = data;

	return await ShiftSettings.create({
		name: 'Default Attendance Setting',
		isDefault: true,
		companyId,
		code: 'Default',
		startTime: '08:00',
		endTime: '18:00',
		clockInLimit: 1,
		clockOutLimit: 1,
		breakLimit: 3,
		clockInDelay: 15,
		clockOutDelay: 120,
		tardinessMinutes: 10,
		tardinessMinutesPerMonth: 30,
		workLocationCheckEnabled: false,
		weekStartsFrom: 'monday',
		allowedAttendanceDays: [
			'monday',
			'tuesday',
			'wednesday',
			'thursday',
			'friday',
			'saturday',
		],
	});
};

module.exports = {
	ShiftSettings,
	generateDefaultShiftSetting,
};
