const mongoose = require('mongoose');
const mongooseDelete = require('mongoose-delete');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');
const autoPopulate = require('mongoose-autopopulate');

const qualificationTypes = {
	UNDER_GRADUATE: 'UNDER_GRADUATE',
	GRADUATE: 'GRADUATE',
	POST_GRADUATE: 'POST_GRADUATE',
	NO_FORMAL_EDUCATION: 'NO_FORMAL_EDUCATION',
};

const EducationDetailsSchema = new mongoose.Schema(
	{
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
		instituteName: {
			type: String,
			// required: [true, 'Institute Name is required'],
			trim: true,
		},
		qualification: {
			type: String,
			required: [true, 'Qualification is required'],
			enum: Object.values(qualificationTypes),
			trim: true,
		},
		grade: {
			type: String,
			trim: true,
		},
		startDate: {
			type: Date,
			// required: [true, 'Start Date is required'],
		},
		endDate: {
			type: Date,
			// required: [true, 'End Date is required'],
			validate: {
				validator: function (value) {
					return !this.startDate || value > this.startDate;
				},
				message: 'End Date must be after Start Date',
			},
		},
		document: {
			type: String,
			trim: true,
		},
	},
	{ timestamps: true }
);

EducationDetailsSchema.plugin(mongooseDelete, {
	deletedAt: true,
	overrideMethods: 'all',
	deletedBy: true,
});
EducationDetailsSchema.plugin(aggregatePaginate);
EducationDetailsSchema.plugin(autoPopulate);

const EducationDetails = mongoose.model(
	'EducationDetails',
	EducationDetailsSchema
);
module.exports = {
	qualificationTypes,
	EducationDetails,
};
