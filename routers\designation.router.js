const express = require('express');
const router = express.Router();

// Controllers
const {
	getAllDesignations,
	addDesignation,
	updateDesignation,
	deleteDesignation,
} = require('../controllers/designation.controller');

// Middlewares
const {
	authenticationMiddleware,
	addDesignationMiddleware,
	updateDesignationMiddleware,
} = require('../middlewares');

const {
	deleteDesignationMiddleware,
} = require('../middlewares/company-details.middleware');

/**
 * @route   GET /
 * @desc    Get all designations
 * @access  Private (Authenticated)
 */
router.route('/').get(authenticationMiddleware, getAllDesignations);

/**
 * @route   POST /
 * @desc    Add a new designation
 * @access  Private (Authenticated)
 */
router
	.route('/')
	.post(authenticationMiddleware, addDesignationMiddleware, addDesignation);

/**
 * @route   PATCH /
 * @desc    Update an existing designation
 * @access  Private (Authenticated)
 */
router
	.route('/')
	.patch(
		authenticationMiddleware,
		updateDesignationMiddleware,
		updateDesignation
	);

/**
 * @route   PATCH //remove
 * @desc    Soft delete a designation
 * @access  Private (Authenticated)
 */
router
	.route('/remove')
	.patch(
		authenticationMiddleware,
		deleteDesignationMiddleware,
		deleteDesignation
	);

module.exports = router;
