const express = require('express');
const router = express.Router();

// Controllers
const {
	getAllCountries,
	getCountryById,
	getCountryCurrency,
	getSearchedCities,
	getAllCurrencies,
	getAllDialCodes,
	getCountryCities,
} = require('../controllers/country.controller');

// Middlewares
const {
	authenticationMiddleware,
	validateCountryId,
} = require('../middlewares');
const { validateDebouncedInputCities } = require('../middlewares');

/**
 * @route   GET /
 * @desc    Get all countries
 * @access  Public
 */
router.route('/').get(getAllCountries);

/**
 * @route   GET /dial-codes
 * @desc    Get all country dial codes
 * @access  Private (requires authentication)
 */
router.route('/dial-codes').get(authenticationMiddleware, getAllDialCodes);

/**
 * @route   GET /search/cities
 * @desc    Search cities (query params)
 * @access  Private (requires authentication)
 */
router
	.route('/search/cities')
	.get(
		authenticationMiddleware,
		validateDebouncedInputCities,
		getSearchedCities
	);

/**
 * @route   GET /cities/:countryName
 * @desc    Get all cities for a country
 * @access  Private (requires authentication)
 */
router
	.route('/cities/:countryName')
	.get(authenticationMiddleware, getCountryCities);

/**
 * @route   GET /currencies
 * @desc    Get all currencies
 * @access  Private (requires authentication)
 */
router.route('/currencies').get(authenticationMiddleware, getAllCurrencies);

/**
 * @route   GET /currencies/:id
 * @desc    Get currency for a country by ID
 * @access  Private (requires authentication)
 */
router
	.route('/currencies/:id')
	.get(authenticationMiddleware, validateCountryId, getCountryCurrency);

module.exports = router;
