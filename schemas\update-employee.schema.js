const z = require('zod');
const dayjs = require('dayjs');

const updateEmployeePersonalDetailsSchema = z.object({
	employeeId: z
		.string()
		.nonempty('Employee ID is required')
		.regex(/^[a-f\d]{24}$/i, 'Invalid Employee ID format'),

	email: z.string().nonempty('Email is required').email('Invalid email format'),

	nameOnNRIC: z
		.string()
		.nonempty('Full Name as per NRIC is required')
		.min(3, 'Full Name must be at least 3 characters long')
		.max(50, 'Full Name must be at most 50 characters long')
		.regex(/^[a-zA-Z\s]+$/, 'Full Name must contain only letters and spaces'),

	countryDialCode: z.string().nonempty('Country dial code is required'),

	mobile: z
		.string()
		.nonempty('Mobile number is required')
		.min(8, 'Mobile number must be at least 8 digits long')
		.max(15, 'Mobile number must be at most 15 digits long')
		.regex(/^\d+$/, 'Mobile number must contain only numbers'),

	gender: z.enum(['male', 'female', 'other'], {
		errorMap: () => ({
			message: 'Gender must be either male, female, or other',
		}),
	}),

	dob: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, 'Date of Birth must be in YYYY-MM-DD format')
		.transform((val) => new Date(val)),

	age: z
		.string()
		.refine((val) => {
			const age = parseInt(val);
			return !isNaN(age) && age >= 18 && age <= 100;
		})
		.optional(),

	nationality: z
		.string()
		.nonempty('Nationality is required')
		.regex(/^[a-f\d]{24}$/i, 'Invalid Nationality ID format'),

	residentialStatus: z.enum(
		[
			'Singapore Citizen',
			'Singapore PR',
			'Employment Pass',
			'SPass',
			'Work Permit',
			'LOC',
		],
		{
			errorMap: () => ({
				message:
					'Residential Status must be one of: Singapore Citizen, Singapore PR, Employment Pass, SPass, Work Permit, or LOC',
			}),
		}
	),

	icFinPrefix: z.string().optional(),

	icFinNumber: z
		.string()
		.nonempty('IC/FIN Number is required')
		.min(9, 'IC/FIN Number must be exactly 9 characters long')
		.max(9, 'IC/FIN Number must be exactly 9 characters long')
		.regex(
			/^[STFGM][0-9]{7}[A-Z]$/,
			'IC/FIN number must be exactly 9 characters long with first and last character in upper case and remaining seven between the uppercase alphabets will be numerical'
		),

	issueDate: z
		.string()
		// .regex(/^\d{4}-\d{2}-\d{2}$/, "Issue Date must be in YYYY-MM-DD format")
		.transform((val) => dayjs(val))
		.optional(),

	religion: z
		.string()
		.nonempty('Religion is required')
		.min(2, 'Religion must be at least 2 characters long')
		.max(50, 'Religion must be at most 50 characters long')
		.regex(/^[a-zA-Z\s]+$/, 'Religion must contain only letters and spaces'),

	race: z
		.string()
		.nonempty('Race is required')
		.min(2, 'Race must be at least 2 characters long')
		.max(50, 'Race must be at most 50 characters long')
		.regex(
			/^[a-zA-Z\s-]+$/,
			'Race must contain only letters, spaces, and dashes'
		),

	country: z
		.string()
		.nonempty('Country is required in address')
		.regex(/^[a-f\d]{24}$/i, 'Invalid Country ID format'),

	postalCode: z
		.string()
		.nonempty('Postal Code is required')
		.regex(/^\d{6}$/, 'Postal Code must be exactly 6 digits'),

	streetName: z
		.string()
		.nonempty('Street Name is required')
		.min(2, 'Street Name must be at least 2 characters long')
		.max(100, 'Street Name must be at most 100 characters long'),

	houseNo: z
		.string()
		.nonempty('House Number is required')
		.min(1, 'House Number must be at least 1 character long')
		.max(10, 'House Number must be at most 10 characters long'),

	levelNo: z
		.string()
		.min(1, 'Level Number must be at least 1 character long')
		.max(5, 'Level Number must be at most 5 characters long')
		.optional(),

	unitNo: z
		.string()
		.min(1, 'Unit Number must be at least 1 character long')
		.max(5, 'Unit Number must be at most 5 characters long')
		.optional(),

	address: z
		.string()
		.nonempty('Address is required')
		.min(5, 'Address must be at least 5 characters long')
		.max(200, 'Address must be at most 200 characters long')
		.regex(
			/^[a-zA-Z0-9\s,.\-#]+$/,
			'Address must contain only letters, numbers, spaces, commas, periods, hashtags and hyphens'
		),
});

const updateEmployeeFamilyDetailsSchema = z.object({
	employeeId: z
		.string()
		.nonempty('Employee ID is required')
		.regex(/^[a-f\d]{24}$/i, 'Invalid Employee ID format'),
	maritalStatus: z.enum(['single', 'married', 'other', 'prefer-not-to-say'], {
		errorMap: () => ({
			message: 'Please select a valid marital status',
		}),
	}),
	spouseName: z.string().nullable().optional(),
	spouseEmploymentStatus: z
		.enum(['employed', 'unemployed', 'self-employed', 'retired', 'student'], {
			errorMap: () => ({
				message: 'Please select a valid employment status',
			}),
		})
		.nullable()
		.optional(),
	children: z
		.array(
			z.object({
				name: z.string().nonempty('Name is required'),
				dob: z
					.string()
					.regex(/^\d{4}-\d{2}-\d{2}$/, 'Date of Birth is required'),
				age: z
					.number()
					.min(0, 'Age must be a positive number')
					.max(100, 'Age must be below 100')
					.optional(),
				nationality: z
					.string()
					.nonempty('Nationality is required')
					.min(4, 'Nationality must be at least 4 characters long')
					.max(50, 'Nationality must be at most 50 characters long')
					.regex(
						/^[a-zA-Z\s]+$/,
						'Nationality must contain only letters and spaces'
					),
			})
		)
		.optional(),
});

const updateEmployeeEducationDetailsSchema = z.object({
	employeeId: z
		.string()
		.nonempty('Employee ID is required')
		.regex(/^[a-f\d]{24}$/i, 'Invalid Employee ID format'),
	education: z.array(
		z.object({
			instituteName: z
				.string()
				.max(100, 'Institute name must be at most 100 characters long')
				.nullable(),
			qualification: z
				.enum(['UNDER_GRADUATE', 'POST_GRADUATE', 'NO_FORMAL_EDUCATION'], {
					errorMap: () => ({
						message: 'Please select a valid qualification',
					}),
				})
				.nullable(),
			grade: z
				.string()
				.max(10, 'Grade must be at most 10 characters long')
				.nullable(),
			startDate: z
				.string()
				.regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date is required')
				.nullable(),
			endDate: z
				.string()
				.regex(/^\d{4}-\d{2}-\d{2}$/, 'End date is required')
				.nullable(),
			_id: z.string().optional(),
		})
	),
	deletedEducation: z.array(z.string()).optional(),
	skills: z.object({
		hardSkills: z.array(z.string()).default([]),
		softSkills: z.array(z.string()).default([]),
	}),
});

const updateEmployeeExperienceDetailsSchema = z.object({
	employeeId: z
		.string()
		.nonempty('Employee ID is required')
		.regex(/^[a-f\d]{24}$/i, 'Invalid Employee ID format'),
	experience: z.array(
		z.object({
			companyName: z.string().min(1, 'Company name is required'),
			designation: z.string().min(1, 'Designation is required'),
			location: z.string().min(1, 'Location is required'),
			periodFrom: z
				.string()
				.regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date is required'),
			periodTo: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'End date is required'),
			reasonForLeaving: z.string().optional(),
			_id: z.string().optional(),
		})
	),
	deletedExperience: z.array(z.string()).optional(),
});

const updateEmployeeContactDetailsSchema = z.object({
	employeeId: z
		.string()
		.nonempty('Employee ID is required')
		.regex(/^[a-f\d]{24}$/i, 'Invalid Employee ID format'),
	contacts: z
		.array(
			z
				.object({
					type: z.enum(['emergency', 'reference'], {
						errorMap: () => ({
							message: "Contact type must be either 'emergency' or 'reference'",
						}),
					}),
					name: z.string().min(1, 'Name is required'),
					relationship: z.string().min(1, 'Relationship is required'),
					countryDialCode: z.string().min(1, 'Country dial code is required'),
					phone: z.string().min(1, 'Phone number is required'),
					email: z.string().email('Invalid email address'),
					_id: z.string().optional(),
				})
				.superRefine((data, ctx) => {
					if (data.type === 'emergency' && !data.name) {
						ctx.addIssue({
							code: z.ZodIssueCode.custom,
							message: 'Name is required for emergency contacts',
							path: ['name'],
						});
					}
					if (data.type === 'reference' && !data.email) {
						ctx.addIssue({
							code: z.ZodIssueCode.custom,
							message: 'Email is required for reference contacts',
							path: ['email'],
						});
					}
				})
		)
		.min(1, 'At least one contact is required'),
	deletedContacts: z.array(z.string()).optional(),
});

const updateEmployeeEmploymentDetailsSchema = z.object({
	employeeId: z
		.string()
		.nonempty('Employee ID is required')
		.regex(/^[a-f\d]{24}$/i, 'Invalid Employee ID format'),
	source: z.enum(['staff-recommendation', 'job-advertisement'], {
		errorMap: () => ({
			message:
				"Source must be either 'staff-recommendation' or 'job-advertisement'",
		}),
	}),
	businessUnit: z.string().min(1, 'Business unit is required'),
	department: z.string().min(1, 'Department is required'),
	designation: z.string().min(1, 'Designation is required'),
	employmentType: z.enum(['part-time', 'full-time'], {
		errorMap: () => ({
			message: "Employment type must be either 'part-time' or 'full-time'",
		}),
	}),
	probationPeriod: z.string().min(1, 'Probation period is required'),
	overTimeEligible: z.boolean().default(false),
	workSchedule: z.enum(['shifts', 'generic']).optional(),
	workingDays: z
		.enum(['5_DAYS', '5.5_DAYS', '6_DAYS', 'ALTERNATE_SATURDAYS'])
		.optional(),
	workingHours: z.enum(['4', '6', '8', '10', '12']).optional(),
	firstOffDay: z.string().optional(),
	secondOffDay: z.string().optional(),
	halfDay: z.enum(['FIRST_HALF', 'SECOND_HALF']).optional(),
});

const updateEmployeeEarningsDetailsSchema = z
	.object({
		basicPay: z.object({
			currency: z.string(),
			amount: z.number().positive('Basic pay cannot be 0 or negative'),
		}),

		paymentMode: z.enum(['cash/cheque', 'bank']),

		frequency: z.enum(['daily', 'weekly', 'monthly']),
		payBasis: z.enum(['hourly', 'daily', 'weekly', 'monthly']),

		dailyRate: z.number().positive('Daily rate cannot be 0 or negative'),
		hourlyRate: z.number().positive('Hourly rate cannot be 0 or negative'),
		weeklyRate: z.number().positive('Weekly rate cannot be 0 or negative'),
		yearlyRate: z.number().positive('Yearly rate cannot be 0 or negative'),
		overtimeRate: z.number('Overtime rate cannot be 0 or negative'),

		isSalaryAdvanceEligible: z.boolean().default(false),
		salaryAdvance: z
			.number()
			.nonnegative('Salary Advance cannot be negative')
			.optional(),

		// Bank details — optional by default, but required in superRefine
		bankName: z.string().optional(),
		accountNumber: z.string().optional(),
		accountHolderName: z.string().optional(),
		bankCode: z.string().optional(),
		swiftBIC: z.string().optional(),
		branchCode: z.string().optional(),
	})
	.superRefine((data, ctx) => {
		// Salary advance validation
		if (data.isSalaryAdvanceEligible) {
			if (!data.salaryAdvance) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'Salary advance amount is required',
					path: ['salaryAdvance'],
				});
			}
			if (data.basicPay.amount === 0 && data.salaryAdvance > 0) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'Please enter basic pay amount first',
					path: ['salaryAdvance'],
				});
			}
			if (data.salaryAdvance > data.basicPay.amount) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'Salary advance amount cannot be greater than basic pay',
					path: ['salaryAdvance'],
				});
			}
		}

		// Bank details conditional validation
		if (data.paymentMode === 'bank') {
			const bankFields = [
				'bankName',
				'accountNumber',
				'accountHolderName',
				'bankCode',
				'swiftBIC',
				'branchCode',
			];

			bankFields.forEach((field) => {
				if (!data[field]) {
					ctx.addIssue({
						code: z.ZodIssueCode.custom,
						message: `${field} is required when payment mode is bank`,
						path: [field],
					});
				}
			});
		}
	});

const updateEmployeeBenefitDetailsSchema = z.object({
	eligibleForOffInLieu: z.boolean().default(false),
	holidayGroups: z
		.array(z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format'))
		.optional(),
	leaveGroups: z
		.array(z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format'))
		.optional(),
});

module.exports = {
	updateEmployeePersonalDetailsSchema,
	updateEmployeeFamilyDetailsSchema,
	updateEmployeeEducationDetailsSchema,
	updateEmployeeExperienceDetailsSchema,
	updateEmployeeContactDetailsSchema,
	updateEmployeeEmploymentDetailsSchema,
	updateEmployeeEarningsDetailsSchema,
	updateEmployeeBenefitDetailsSchema,
};
