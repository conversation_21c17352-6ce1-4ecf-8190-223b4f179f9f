const { BadRequestError } = require('../errors');
const {
	personalDetailsSchema,
	qualificationsSchema,
	contactDetailsSchema,
	employmentDetailsSchema,
	earningsDetailsSchema,
	benefitsSchema,
	verifyLinkTokenSchema,
	onboardEmployeeLinkSchema,
} = require('../schemas/employee.schema');
const {
	updateEmployeePersonalDetailsSchema,
	updateEmployeeFamilyDetailsSchema,
	updateEmployeeEducationDetailsSchema,
	updateEmployeeExperienceDetailsSchema,
	updateEmployeeContactDetailsSchema,
	updateEmployeeEmploymentDetailsSchema,
	updateEmployeeEarningsDetailsSchema,
	updateEmployeeBenefitDetailsSchema,
} = require('../schemas/update-employee.schema');
const {
	imageMediaSchema,
	imageAndPdfMediaSchema,
} = require('./multerMiddleware');
const dayjs = require('dayjs');

const employeePersonalDetailsMiddleware = (req, res, next) => {
	const children = req.body.children ? JSON.parse(req.body.children) : [];
	req.body.children = children;
	let expiryDateReminder;
	if (req.body.expiryDateReminder) {
		expiryDateReminder = dayjs(req.body.expiryDate)
			.subtract(parseInt(req.body.expiryDateReminder), 'day')
			.format('YYYY-MM-DD');
	}
	const result = personalDetailsSchema.safeParse(req.body);
	req.body.expiryDateReminder = expiryDateReminder;
	if (req.file) {
		const mediaResult = imageMediaSchema.safeParse({
			mimetype: req.file?.mimetype,
			size: req.file?.size,
		});
		if (!mediaResult.success) {
			throw new BadRequestError('Invalid File', mediaResult.error.format());
		}
	}

	if (!result.success) {
		throw new BadRequestError(
			'Invalid Personal Details',
			result.error.format()
		);
	}
	next();
};

const educationAndSkillsMiddleware = (req, res, next) => {
	const result = qualificationsSchema.safeParse(req.body);
	if (req.files.length > 0) {
		const invalidFiles = req.files.filter((file) => {
			const mediaResult = imageAndPdfMediaSchema.safeParse({
				mimetype: file?.mimetype,
				size: file?.size,
			});
			return !mediaResult.success;
		});
		if (invalidFiles.length > 0) {
			throw new BadRequestError(
				'Invalid File',
				invalidFiles.map((file) => {
					return imageAndPdfMediaSchema
						.safeParse({
							mimetype: file?.mimetype,
							size: file?.size,
						})
						.error.format();
				})
			);
		}
	}
	if (!result.success) {
		throw new BadRequestError(
			'Invalid Education and Skills',
			result.error.format()
		);
	}
	next();
};

const employeeContactDetailsMiddleware = (req, res, next) => {
	const result = contactDetailsSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError('Invalid Contact Details', result.error.format());
	}
	next();
};

const employeeEmploymentDetailsMiddleware = (req, res, next) => {
	const result = employmentDetailsSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid Employment Details',
			result.error.format()
		);
	}
	next();
};

const employeeEarningsDetailsMiddleware = (req, res, next) => {
	const result = earningsDetailsSchema.safeParse(req.body);
	// console.log(` employeeEarningsDetailsMiddleware - req.body:`, req.body);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid Earnings Details',
			result.error.format()
		);
	}
	next();
};

const employeeBenefitsDetailsMiddleware = (req, res, next) => {
	const result = benefitsSchema.safeParse(req.body);
	if (!result.success) {
		throw new BadRequestError(
			'Invalid Benefits Details',
			result.error.format()
		);
	}
	next();
};

// Updation middlewares
const updateEmployeePersonalDetailsMiddleware = (req, res, next) => {
	const result = updateEmployeePersonalDetailsSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError(
			'Invalid Personal Details',
			result.error.format()
		);
	}
	next();
};

const updateEmployeeFamilyDetailsMiddleware = (req, res, next) => {
	const result = updateEmployeeFamilyDetailsSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError('Invalid Family Details', result.error.format());
	}
	next();
};

const updateEmployeeEducationDetailsMiddleware = (req, res, next) => {
	const result = updateEmployeeEducationDetailsSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError(
			'Invalid Education Details',
			result.error.format()
		);
	}
	next();
};

const updateEmployeeExperienceDetailsMiddleware = (req, res, next) => {
	const result = updateEmployeeExperienceDetailsSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError(
			'Invalid Experience Details',
			result.error.format()
		);
	}
	next();
};

const updateEmployeeContactDetailsMiddleware = (req, res, next) => {
	const result = updateEmployeeContactDetailsSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError('Invalid Contact Details', result.error.format());
	}
	next();
};

const updateEmployeeEmploymentDetailsMiddleware = (req, res, next) => {
	const result = updateEmployeeEmploymentDetailsSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError(
			'Invalid Employment Details',
			result.error.format()
		);
	}
	next();
};

const updateEmployeeEarningsDetailsMiddleware = (req, res, next) => {
	const result = updateEmployeeEarningsDetailsSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError(
			'Invalid Earnings Details',
			result.error.format()
		);
	}
	next();
};

const onboardEmployeeUsingLinkMiddleware = (req, res, next) => {
	const result = onboardEmployeeLinkSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError(
			'Invalid Onboard Employee Link Details',
			result.error.format()
		);
	}
	next();
};

const verifyLinkOnboardRequestMiddleware = (req, res, next) => {
	const result = verifyLinkTokenSchema.safeParse(req.params);

	if (!result.success) {
		throw new BadRequestError(
			'Invalid Verify Link Details',
			result.error.format()
		);
	}
	next();
};

const updateEmployeeBenefitDetailsMiddleware = (req, res, next) => {
	const result = updateEmployeeBenefitDetailsSchema.safeParse(req.body);

	if (!result.success) {
		throw new BadRequestError('Invalid Benefit Details', result.error.format());
	}
	next();
};

module.exports = {
	employeePersonalDetailsMiddleware,
	educationAndSkillsMiddleware,
	employeeContactDetailsMiddleware,
	employeeEmploymentDetailsMiddleware,
	employeeEarningsDetailsMiddleware,
	employeeBenefitsDetailsMiddleware,

	updateEmployeePersonalDetailsMiddleware,
	updateEmployeeFamilyDetailsMiddleware,
	updateEmployeeEducationDetailsMiddleware,
	updateEmployeeExperienceDetailsMiddleware,
	updateEmployeeContactDetailsMiddleware,
	updateEmployeeEmploymentDetailsMiddleware,
	updateEmployeeEarningsDetailsMiddleware,

	onboardEmployeeUsingLinkMiddleware,
	verifyLinkOnboardRequestMiddleware,
	updateEmployeeBenefitDetailsMiddleware,
};
